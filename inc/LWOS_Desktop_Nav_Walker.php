<?php
/**
 * Custom Nav Walker for Desktop Menu with Tailwind CSS
 *
 * @package LWOS_2025
 */

if ( ! class_exists( 'LWOS_Desktop_Nav_Walker' ) ) {
	class LWOS_Desktop_Nav_Walker extends Walker_Nav_Menu {
		/**
		 * Starts the list before the elements are added.
		 *
		 * @param string   $output Used to append additional content (passed by reference).
		 * @param int      $depth  Depth of menu item. Used for padding.
		 * @param stdClass $args   An object of wp_nav_menu() arguments.
		 */
		public function start_lvl( &$output, $depth = 0, $args = null ) {
			if ( isset( $args->item_spacing ) && 'discard' === $args->item_spacing ) {
				$t = '';
				$n = '';
			} else {
				$t = "\t";
				$n = "\n";
			}
			$indent = str_repeat( $t, $depth );

			// Base classes for all sub-menus (structure and generic styling)
			$base_structural_classes = array( 'sub-menu', 'absolute', 'w-56', 'divide-y', 'divide-gray-300', 'rounded-md', 'shadow-lg', 'ring-1', 'ring-black', 'ring-opacity-5', 'focus:outline-none', 'hidden', 'group-hover:block', 'z-50' );

			// Determine background color based on depth
			$bg_color_class = 'bg-white'; // Default for depth 0
			if ( $depth === 1 ) { // Second level dropdown (sub-submenu)
				$bg_color_class = 'bg-gray-100'; // Darker
			} elseif ( $depth >= 2 ) { // Third level and deeper
				$bg_color_class = 'bg-gray-200'; // Even darker
			}

			// Positioning classes based on depth
			if ( $depth > 0 ) {
				// Sub-submenu (opens to the right of its parent LI)
				$position_classes = array( 'left-full', 'top-0', 'sub-submenu-level' );
			} else {
				// First level submenu (opens downwards)
				$position_classes = array( 'left-0', 'mt-1' );
			}

			$all_classes = array_merge( $base_structural_classes, array( $bg_color_class ), $position_classes );

			$output .= "{$n}{$indent}<ul class=\"" . esc_attr( implode( ' ', $all_classes ) ) . "\">{$n}";
		}

		/**
		 * Starts the element output.
		 *
		 * @param string   $output Used to append additional content (passed by reference).
		 * @param WP_Post  $item   Menu item data object.
		 * @param int      $depth  Depth of menu item. Used for padding.
		 * @param stdClass $args   An object of wp_nav_menu() arguments.
		 * @param int      $id     Current item ID.
		 */
		public function start_el( &$output, $item, $depth = 0, $args = null, $id = 0 ) {
			if ( isset( $args->item_spacing ) && 'discard' === $args->item_spacing ) {
				$t = '';
				$n = '';
			} else {
				$t = "\t";
				$n = "\n";
			}
			$indent = ( $depth ) ? str_repeat( $t, $depth ) : '';

			$classes   = empty( $item->classes ) ? array() : (array) $item->classes;
			$classes[] = 'menu-item-' . $item->ID;

			// Add 'group' class to LI if it has children for group-hover to work on submenu
			if ( $args->walker->has_children ) {
				$classes[] = 'group';
				$classes[] = 'relative'; // Needed for absolute positioning of submenu
			}

			$args = apply_filters( 'nav_menu_item_args', $args, $item, $depth );

			$class_names = implode( ' ', apply_filters( 'nav_menu_css_class', array_filter( $classes ), $item, $args, $depth ) );
			$class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

			$id = apply_filters( 'nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args, $depth );
			$id = $id ? ' id="' . esc_attr( $id ) . '"' : '';

			$output .= $indent . '<li' . $id . $class_names . '>';

			$atts           = array();
			$atts['title']  = ! empty( $item->attr_title ) ? $item->attr_title : '';
			$atts['target'] = ! empty( $item->target ) ? $item->target : '';
			if ( '_blank' === $item->target && empty( $item->xfn ) ) {
				$atts['rel'] = 'noopener noreferrer';
			} else {
				$atts['rel'] = $item->xfn;
			}
			$atts['href']         = ! empty( $item->url ) ? $item->url : '';
			$atts['aria-current'] = $item->current ? 'page' : '';

			// Add Tailwind classes to links
			$link_classes = array( 'text-black', 'visited:text-black', 'hover:text-gray-700', 'px-3', 'py-2', 'rounded-md', 'text-base', 'font-medium' );
			if ($depth > 0) { // Submenu item links
				$link_classes = array( 'text-black', 'visited:text-black', 'hover:text-gray-700', 'block', 'px-4', 'py-2', 'text-sm' );
			}

			$atts['class'] = implode( ' ', $link_classes);

			$atts = apply_filters( 'nav_menu_link_attributes', $atts, $item, $args, $depth );

			$attributes = '';
			foreach ( $atts as $attr => $value ) {
				if ( ! empty( $value ) ) {
					$value       = ( 'href' === $attr ) ? esc_url( $value ) : esc_attr( $value );
					$attributes .= ' ' . $attr . '="' . $value . '"';
				}
			}

			$item_output  = $args->before;
			$item_output .= '<a' . $attributes . '>';
			$item_output .= $args->link_before . apply_filters( 'the_title', $item->title, $item->ID ) . $args->link_after;
			$item_output .= '</a>';
			$item_output .= $args->after;

			$output .= apply_filters( 'walker_nav_menu_start_el', $item_output, $item, $depth, $args );
		}
	}
} 