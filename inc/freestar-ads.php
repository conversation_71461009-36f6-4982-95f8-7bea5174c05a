<?php
/**
 * Freestar ad slot definitions & hooks for LWOS 2025 theme.
 *
 * HOW TO CHANGE / ADD ADS
 * -----------------------
 * 1. Each ad position is stored once in the \$ad_slots array below (key = slot ID, value = full HTML provided by Freestar).
 * 2. To swap ad code, simply edit the HTML string for the desired key or add a new key => value pair.
 * 3. In any template you can output the ad with:
 *        do_action( 'lwos_ad_' . SLOT_ID );
 *    e.g.  do_action( 'lwos_ad_lwos_header' );
 * 4. No template edits are needed after the hook is in place – all markup is centralised here to keep things DRY.
 */

if ( ! function_exists( 'lwos_register_freestar_ad_hooks' ) ) {
	function lwos_register_freestar_ad_hooks() {
		// Map each provided slot ID to the raw HTML snippet from Freestar.
		$ad_slots = array(
			'lwos_incontent_5'  => '<div align="center" data-freestar-ad="__336x280 __300x250" id="lwos_incontent_5">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_incontent_5", slotId: "lwos_incontent_5" });
  </script>
</div>',
			'lwos_mpu_btf'     => '<div align="center" data-freestar-ad="__336x280" id="lwos_mpu_btf">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_mpu_btf", slotId: "lwos_mpu_btf" });
  </script>
</div>',
			'lwos_bouncex'      => '<div align="center" id="lwos_bouncex">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_bouncex", slotId: "lwos_bouncex" });
  </script>
</div>',
			'lwos_right_rail_3' => '<div align="center" data-freestar-ad="__300x600" id="lwos_right_rail_3">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_right_rail_3", slotId: "lwos_right_rail_3" });
  </script>
</div>',
			'lwos_incontent_1'  => '<div align="center" data-freestar-ad="__336x280 __300x250" id="lwos_incontent_1">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_incontent_1", slotId: "lwos_incontent_1" });
  </script>
</div>',
			'lwos_header'       => '<div align="center" data-freestar-ad="__320x100 __728x90" id="lwos_header">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_header", slotId: "lwos_header" });
  </script>
</div>',
			'lwos_incontent_3'  => '<div align="center" data-freestar-ad="__336x280 __300x250" id="lwos_incontent_3">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_incontent_3", slotId: "lwos_incontent_3" });
  </script>
</div>',
			'lwos_incontent_4'  => '<div align="center" data-freestar-ad="__336x280 __300x250" id="lwos_incontent_4">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_incontent_4", slotId: "lwos_incontent_4" });
  </script>
</div>',
			'lwos_right_rail_2' => '<div align="center" data-freestar-ad="__300x600" id="lwos_right_rail_2">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_right_rail_2", slotId: "lwos_right_rail_2" });
  </script>
</div>',
			'lwos_right_rail_1' => '<div align="center" data-freestar-ad="__300x250" id="lwos_right_rail_1">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_right_rail_1", slotId: "lwos_right_rail_1" });
  </script>
</div>',
			'lwos_incontent_2'  => '<div align="center" data-freestar-ad="__336x280 __300x250" id="lwos_incontent_2">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_incontent_2", slotId: "lwos_incontent_2" });
  </script>
</div>',
			'lwos_mpu_1'        => '<div align="center" data-freestar-ad="__336x280 __300x250" id="lwos_mpu_1">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_mpu_1", slotId: "lwos_mpu_1" });
  </script>
</div>',
			'lwos_billboard_BTF'=> '<div align="center" data-freestar-ad="__336x280 __970x250" id="lwos_billboard_BTF">
  <script data-cfasync="false" type="text/javascript">
    freestar.config.enabled_slots.push({ placementName: "lwos_billboard_BTF", slotId: "lwos_billboard_BTF" });
  </script>
</div>',
		);

		foreach ( $ad_slots as $slot_id => $markup ) {
			// Capture current markup in its own variable to avoid late binding inside closure.
			$code_copy = $markup;
			add_action( 'lwos_ad_' . $slot_id, function() use ( $code_copy ) {
				echo $code_copy; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped -- trusted ad code
			} );
		}
	}
	add_action( 'init', 'lwos_register_freestar_ad_hooks' );
} 