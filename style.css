/*!
Theme Name: LWOS 2025
Theme URI: https://underscores.me/
Author: <PERSON>, CTO LWOS
Author URI: https://automattic.com/
Description: Lightweight theme built for LWOS main site
Version: 1.0.1
Tested up to: 5.4
Requires PHP: 7.2
License: GNU General Public License v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: lwos-2025
Tags: one-column, two-columns, right-sidebar, flexible-header, accessibility-ready, custom-colors, custom-header, custom-menu, custom-logo, editor-style, featured-images, footer-widgets, post-formats, rtl-language-support, sticky-post, theme-options, threaded-comments, translation-ready

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned.

LWOS 2025 is based on Underscores https://underscores.me/, (C) 2012-2020 Automattic, Inc.
Underscores is distributed under the terms of the GNU GPL v2 or later.

Normalizing styles have been helped along thanks to the fine work of
<PERSON> and <PERSON> https://necolas.github.io/normalize.css/
*/

/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Generic
	- Normalize
	- Box sizing
# Base
	- Typography
	- Elements
	- Links
	- Forms
## Layouts
# Components
	- Navigation
	- Posts and pages
	- Comments
	- Widgets
	- Media
	- Captions
	- Galleries
# plugins
	- Jetpack infinite scroll
# Utilities
	- Accessibility
	- Alignments

--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Generic
--------------------------------------------------------------*/

/* Normalize
--------------------------------------------- */

/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* Document
	 ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
	line-height: 1.15;
	-webkit-text-size-adjust: 100%;
}

/* Sections
	 ========================================================================== */

/**
 * Remove the margin in all browsers.
 */
body {
	margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
	display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
	font-size: 2em;
	margin: 0.67em 0;
}

/* Grouping content
	 ========================================================================== */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
	box-sizing: content-box;
	height: 0;
	overflow: visible;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
	font-family: monospace, monospace;
	font-size: 1em;
}

/* Text-level semantics
	 ========================================================================== */

/**
 * Remove the gray background on active links in IE 10.
 */
a {
	background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
	border-bottom: none;
	text-decoration: underline;
	text-decoration: underline dotted;
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
	font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
	font-family: monospace, monospace;
	font-size: 1em;
}

/**
 * Add the correct font size in all browsers.
 */
small {
	font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

/* Embedded content
	 ========================================================================== */

/**
 * Remove the border on images inside links in IE 10.
 */
img {
	border-style: none;
}

/* Forms
	 ========================================================================== */

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
	font-family: inherit;
	font-size: 100%;
	line-height: 1.15;
	margin: 0;
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
	overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
	text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type="button"],
[type="reset"],
[type="submit"] {
	-webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
	border-style: none;
	padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
	outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
	padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *		`fieldset` elements in all browsers.
 */
legend {
	box-sizing: border-box;
	color: inherit;
	display: table;
	max-width: 100%;
	padding: 0;
	white-space: normal;
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
	vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
	overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type="checkbox"],
[type="radio"] {
	box-sizing: border-box;
	padding: 0;
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
	height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit;
}

/* Interactive
	 ========================================================================== */

/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
	display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
	display: list-item;
}

/* Misc
	 ========================================================================== */

/**
 * Add the correct display in IE 10+.
 */
template {
	display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
	display: none;
}

/* Box sizing
--------------------------------------------- */

/* Inherit box-sizing to more easily change it's value on a component level.
@link http://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/ */
*,
*::before,
*::after {
	box-sizing: inherit;
}

html {
	box-sizing: border-box;
}

/*--------------------------------------------------------------
# Base
--------------------------------------------------------------*/

/* Typography
--------------------------------------------- */
body,
button,
input,
select,
optgroup,
textarea {
	color: #404040;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1rem;
	line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	clear: both;
}

p {
	margin-bottom: 1.5em;
}

dfn,
cite,
em,
i {
	font-style: italic;
}

blockquote {
	margin: 0 1.5em;
}

address {
	margin: 0 0 1.5em;
}

pre {
	background: #eee;
	font-family: "Courier 10 Pitch", courier, monospace;
	line-height: 1.6;
	margin-bottom: 1.6em;
	max-width: 100%;
	overflow: auto;
	padding: 1.6em;
}

code,
kbd,
tt,
var {
	font-family: monaco, consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
}

abbr,
acronym {
	border-bottom: 1px dotted #666;
	cursor: help;
}

mark,
ins {
	background: #fff9c0;
	text-decoration: none;
}

big {
	font-size: 125%;
}

/* Elements
--------------------------------------------- */
body {
	background: #fff;
}

hr {
	background-color: #ccc;
	border: 0;
	height: 1px;
	margin-bottom: 1.5em;
}

ul,
ol {
	margin: 0 0 1.5em 3em;
}

ul {
	list-style: disc;
}

ol {
	list-style: decimal;
}

li > ul,
li > ol {
	margin-bottom: 0;
	margin-left: 1.5em;
}

dt {
	font-weight: 700;
}

dd {
	margin: 0 1.5em 1.5em;
}

/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
	max-width: 100%;
}

img {
	height: auto;
	max-width: 100%;
}

figure {
	margin: 1em 0;
}

table {
	margin: 0 0 1.5em;
	width: 100%;
}

/* Links
--------------------------------------------- */
a {
	color: #4169e1;
}

a:visited {
	color: #800080;
}

a:hover,
a:focus,
a:active {
	color: #191970;
}

a:focus {
	outline: thin dotted;
}

a:hover,
a:active {
	outline: 0;
}

/* Forms
--------------------------------------------- */
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
	border: 1px solid;
	border-color: #ccc #ccc #bbb;
	border-radius: 3px;
	background: #e6e6e6;
	color: rgba(0, 0, 0, 0.8);
	line-height: 1;
	padding: 0.6em 1em 0.4em;
}

button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover {
	border-color: #ccc #bbb #aaa;
}

button:active,
button:focus,
input[type="button"]:active,
input[type="button"]:focus,
input[type="reset"]:active,
input[type="reset"]:focus,
input[type="submit"]:active,
input[type="submit"]:focus {
	border-color: #aaa #bbb #bbb;
}

input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
textarea {
	color: #666;
	border: 1px solid #ccc;
	border-radius: 3px;
	padding: 3px;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="range"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
input[type="time"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="color"]:focus,
textarea:focus {
	color: #111;
}

/* Search form specific styling - override default input styling for dark theme search */
.search-field {
	color: white !important;
	background-color: rgb(31, 41, 55) !important; /* bg-gray-800 */
	border-color: rgb(55, 65, 81) !important; /* border-gray-700 */
}

.search-field:focus {
	color: white !important;
	background-color: rgb(31, 41, 55) !important; /* Keep dark background on focus */
}

select {
	border: 1px solid #ccc;
}

textarea {
	width: 100%;
}

/*--------------------------------------------------------------
# Layouts
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Components
--------------------------------------------------------------*/

/* Navigation
--------------------------------------------- */
.main-navigation {
	display: block;
	width: 100%;
}

.main-navigation ul {
	display: none;
	list-style: none;
	margin: 0;
	padding-left: 0;
}

.main-navigation ul ul {
	box-shadow: 0 3px 3px rgba(0, 0, 0, 0.2);
	float: left;
	position: absolute;
	top: 100%;
	left: -999em;
	z-index: 99999;
}

.main-navigation ul ul ul {
	left: -999em;
	top: 0;
}

.main-navigation ul ul li:hover > ul,
.main-navigation ul ul li.focus > ul {
	display: block;
	left: auto;
}

.main-navigation ul ul a {
	width: 200px;
}

.main-navigation ul li:hover > ul,
.main-navigation ul li.focus > ul {
	left: auto;
}

.main-navigation li {
	position: relative;
}

.main-navigation a {
	display: block;
	text-decoration: none;
}

/* Small menu. */
.menu-toggle,
.main-navigation.toggled ul {
	display: block;
}

@media screen and (min-width: 37.5em) {

	.menu-toggle {
		display: none;
	}

	.main-navigation ul {
		display: flex;
	}
}

.site-main .comment-navigation,
.site-main
.posts-navigation,
.site-main
.post-navigation {
	margin: 0 0 1.5em;
}

.comment-navigation .nav-links,
.posts-navigation .nav-links,
.post-navigation .nav-links {
	display: flex;
}

.comment-navigation .nav-previous,
.posts-navigation .nav-previous,
.post-navigation .nav-previous {
	flex: 1 0 50%;
}

.comment-navigation .nav-next,
.posts-navigation .nav-next,
.post-navigation .nav-next {
	text-align: end;
	flex: 1 0 50%;
}

/* Posts and pages
--------------------------------------------- */
.sticky {
	display: block;
}

.post,
.page {
	margin: 0 0 1.5em;
}

.updated:not(.published) {
	display: none;
}

.page-content,
.entry-content,
.entry-summary {
	margin: 1.5em 0 0;
}

.page-links {
	clear: both;
	margin: 0 0 1.5em;
}

/* Comments
--------------------------------------------- */
.comment-content a {
	word-wrap: break-word;
}

.bypostauthor {
	display: block;
}

/* Widgets
--------------------------------------------- */
.widget {
	margin: 0 0 1.5em;
}

.widget select {
	max-width: 100%;
}

/* Media
--------------------------------------------- */
.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
	border: none;
	margin-bottom: 0;
	margin-top: 0;
	padding: 0;
}

/* Make sure logo link wraps around logo image. */
.custom-logo-link {
	display: inline-block;
}

/* Captions
--------------------------------------------- */
.wp-caption {
	margin-bottom: 1.5em;
	max-width: 100%;
}

.wp-caption img[class*="wp-image-"] {
	display: block;
	margin-left: auto;
	margin-right: auto;
}

.wp-caption .wp-caption-text {
	margin: 0.8075em 0;
}

.wp-caption-text {
	text-align: center;
}

/* Galleries
--------------------------------------------- */
.gallery {
	margin-bottom: 1.5em;
	display: grid;
	grid-gap: 1.5em;
}

.gallery-item {
	display: inline-block;
	text-align: center;
	width: 100%;
}

.gallery-columns-2 {
	grid-template-columns: repeat(2, 1fr);
}

.gallery-columns-3 {
	grid-template-columns: repeat(3, 1fr);
}

.gallery-columns-4 {
	grid-template-columns: repeat(4, 1fr);
}

.gallery-columns-5 {
	grid-template-columns: repeat(5, 1fr);
}

.gallery-columns-6 {
	grid-template-columns: repeat(6, 1fr);
}

.gallery-columns-7 {
	grid-template-columns: repeat(7, 1fr);
}

.gallery-columns-8 {
	grid-template-columns: repeat(8, 1fr);
}

.gallery-columns-9 {
	grid-template-columns: repeat(9, 1fr);
}

.gallery-caption {
	display: block;
}

/*--------------------------------------------------------------
# Plugins
--------------------------------------------------------------*/

/* Jetpack infinite scroll
--------------------------------------------- */

/* Hide the Posts Navigation and the Footer when Infinite Scroll is in use. */
.infinite-scroll .posts-navigation,
.infinite-scroll.neverending .site-footer {
	display: none;
}

/* Re-display the Theme Footer when Infinite Scroll has reached its end. */
.infinity-end.neverending .site-footer {
	display: block;
}

/*--------------------------------------------------------------
# Utilities
--------------------------------------------------------------*/

/* Accessibility
--------------------------------------------- */

/* Text meant only for screen readers. */
.screen-reader-text {
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute !important;
	width: 1px;
	word-wrap: normal !important;
}

.screen-reader-text:focus {
	background-color: #f1f1f1;
	border-radius: 3px;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	clip: auto !important;
	clip-path: none;
	color: #21759b;
	display: block;
	font-size: 0.875rem;
	font-weight: 700;
	height: auto;
	left: 5px;
	line-height: normal;
	padding: 15px 23px 14px;
	text-decoration: none;
	top: 5px;
	width: auto;
	z-index: 100000;
}

/* Do not show the outline on the skip link target. */
#primary[tabindex="-1"]:focus {
	outline: 0;
}

/* Alignments
--------------------------------------------- */
.alignleft {

	/*rtl:ignore*/
	float: left;

	/*rtl:ignore*/
	margin-right: 1.5em;
	margin-bottom: 1.5em;
}

.alignright {

	/*rtl:ignore*/
	float: right;

	/*rtl:ignore*/
	margin-left: 1.5em;
	margin-bottom: 1.5em;
}

.aligncenter {
	clear: both;
	display: block;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 1.5em;
}

/* Custom Menu Styles */
.sub-menu.sub-submenu-level {
    @apply absolute top-0 left-full w-56; /* Removed ml-1 to eliminate gap */
    /* Direct CSS for positioning fallback and initial hidden state */
    position: absolute !important;
    top: 0 !important;
    left: 100% !important;
    margin-left: 0 !important; /* No margin to prevent gap */
    width: 14rem !important;
    display: none !important; /* Ensure it is hidden by default */
}

/* When the parent LI (which has .group and .relative) is hovered, display its direct child .sub-submenu-level */
li.group.relative:hover > ul.sub-menu.sub-submenu-level {
    display: block !important;
}

/* Ensure parent LI of a sub-submenu is relative */
/* This selector targets an LI, that is a child of a .sub-menu, and itself has children */
.sub-menu > .menu-item-has-children.group {
    @apply relative;
     position: relative !important; /* Reinforce */
}

/* Additional hover area bridge - extends the hover area slightly to prevent gaps */
.menu-item-has-children.group > a::after {
    content: '';
    position: absolute;
    top: 0;
    right: -2px; /* Small extension to bridge any potential gap */
    bottom: 0;
    width: 2px;
    z-index: 1;
}

/* Ensure smooth transitions between parent and child menus */
.menu-item-has-children.group:hover > ul.sub-menu.sub-submenu-level,
.sub-menu.sub-submenu-level:hover {
    display: block !important;
}

/* Add a small overlap to ensure no gaps */
.sub-menu.sub-submenu-level {
    left: calc(100% - 1px) !important; /* Slight overlap to prevent gaps */
}

/* Author link styling */
.entry-content a, 
.post-meta a,
.author-link {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;
}

.entry-content a:hover,
.post-meta a:hover,
.author-link:hover {
    color: #2563eb; /* blue-600 */
}

/* Ensure author links in post meta blend with the design */
.text-gray-500 a {
    color: inherit;
}

.text-gray-500 a:hover {
    color: #6B7280;
}

/* Content Styling for Post Content */
.content-styled {
	color: #374151;
	line-height: 1.75;
	font-size: 1.125rem;
}

.content-styled > * {
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
}

.content-styled > :first-child {
	margin-top: 0;
}

.content-styled > :last-child {
	margin-bottom: 0;
}

/* Headings */
.content-styled h1 {
	font-size: 2.25rem;
	font-weight: 800;
	color: #1F2937;
	line-height: 1.1;
	margin-top: 2rem;
	margin-bottom: 1rem;
}

.content-styled h2 {
	font-size: 1.875rem;
	font-weight: 700;
	color: #1F2937;
	line-height: 1.2;
	margin-top: 2rem;
	margin-bottom: 1rem;
}

.content-styled h3 {
	font-size: 1.5rem;
	font-weight: 600;
	color: #1F2937;
	line-height: 1.3;
	margin-top: 1.5rem;
	margin-bottom: 0.75rem;
}

.content-styled h4 {
	font-size: 1.25rem;
	font-weight: 600;
	color: #1F2937;
	line-height: 1.4;
	margin-top: 1.5rem;
	margin-bottom: 0.75rem;
}

.content-styled h5 {
	font-size: 1.125rem;
	font-weight: 600;
	color: #1F2937;
	line-height: 1.4;
	margin-top: 1.25rem;
	margin-bottom: 0.5rem;
}

.content-styled h6 {
	font-size: 1rem;
	font-weight: 600;
	color: #1F2937;
	line-height: 1.4;
	margin-top: 1.25rem;
	margin-bottom: 0.5rem;
}

/* Paragraphs */
.content-styled p {
	margin-top: 1.25rem;
	margin-bottom: 1.25rem;
}

/* Lists */
.content-styled ol,
.content-styled ul {
	margin-top: 1.25rem;
	margin-bottom: 1.25rem;
	padding-left: 1.5rem;
}

.content-styled ul {
	list-style-type: disc;
}

.content-styled ol {
	list-style-type: decimal;
}

.content-styled li {
	margin-top: 0.5rem;
	margin-bottom: 0.5rem;
}

.content-styled ul ul,
.content-styled ul ol,
.content-styled ol ul,
.content-styled ol ol {
	margin-top: 0.75rem;
	margin-bottom: 0.75rem;
}

/* Blockquotes */
.content-styled blockquote {
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
	padding-left: 1.5rem;
	border-left: 4px solid #D1D5DB;
	font-style: italic;
	color: #6B7280;
	background-color: #F9FAFB;
	padding: 1.5rem;
	border-radius: 0.5rem;
}

.content-styled blockquote p {
	margin-top: 0;
	margin-bottom: 0;
}

/* Code */
.content-styled code {
	background-color: #F3F4F6;
	padding: 0.25rem 0.5rem;
	border-radius: 0.375rem;
	font-family: 'Courier New', monospace;
	font-size: 0.875rem;
	color: #DC2626;
}

.content-styled pre {
	background-color: #1F2937;
	color: #F9FAFB;
	padding: 1.5rem;
	border-radius: 0.5rem;
	overflow-x: auto;
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
}

.content-styled pre code {
	background-color: transparent;
	padding: 0;
	color: inherit;
	font-size: 0.875rem;
}

/* Images */
.content-styled img {
	max-width: 100%;
	height: auto;
	border-radius: 0.5rem;
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
}

.content-styled figure {
	margin-top: 2rem;
	margin-bottom: 2rem;
}

.content-styled figure img {
	margin-top: 0;
	margin-bottom: 0;
}

.content-styled figcaption {
	margin-top: 0.75rem;
	font-size: 0.875rem;
	color: #6B7280;
	text-align: center;
	font-style: italic;
}

/* Tables */
.content-styled table {
	width: 100%;
	margin-top: 2rem;
	margin-bottom: 2rem;
	border-collapse: collapse;
	font-size: 0.875rem;
}

.content-styled thead {
	border-bottom: 1px solid #D1D5DB;
}

.content-styled th {
	padding: 0.75rem;
	text-align: left;
	font-weight: 600;
	color: #1F2937;
	background-color: #F9FAFB;
}

.content-styled td {
	padding: 0.75rem;
	border-bottom: 1px solid #E5E7EB;
}

.content-styled tbody tr:hover {
	background-color: #F9FAFB;
}

/* Horizontal Rules */
.content-styled hr {
	margin-top: 3rem;
	margin-bottom: 3rem;
	border: 0;
	border-top: 1px solid #E5E7EB;
}

/* Links */
.content-styled a {
	color: #2563EB;
	text-decoration: underline;
}

.content-styled a:hover {
	color: #1D4ED8;
	text-decoration: none;
}

/* Strong and emphasis */
.content-styled strong {
	font-weight: 600;
	color: #1F2937;
}

.content-styled em {
	font-style: italic;
}

/* WordPress specific elements */
.content-styled .wp-block-quote {
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
	padding-left: 1.5rem;
	border-left: 4px solid #D1D5DB;
	font-style: italic;
	color: #6B7280;
	background-color: #F9FAFB;
	padding: 1.5rem;
	border-radius: 0.5rem;
}

.content-styled .wp-block-image {
	margin-top: 2rem;
	margin-bottom: 2rem;
}

.content-styled .wp-block-image figcaption {
	margin-top: 0.75rem;
	font-size: 0.875rem;
	color: #6B7280;
	text-align: center;
	font-style: italic;
}

/* Page links for paginated posts */
.content-styled .page-links {
	margin-top: 3rem;
	margin-bottom: 2rem;
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	gap: 0.5rem;
}

.content-styled .page-links a,
.content-styled .page-links > span {
	display: inline-block;
	padding: 0.5rem 1rem;
	background-color: #F3F4F6;
	color: #374151;
	text-decoration: none;
	border-radius: 0.375rem;
	transition: background-color 0.2s;
}

.content-styled .page-links a:hover {
	background-color: #E5E7EB;
}

.content-styled .page-links .current {
	background-color: #2563EB;
	color: white;
}

/* Responsive text sizes */
@media (max-width: 768px) {
	.content-styled {
		font-size: 1rem;
	}
	
	.content-styled h1 {
		font-size: 1.875rem;
	}
	
	.content-styled h2 {
		font-size: 1.5rem;
	}
	
	.content-styled h3 {
		font-size: 1.25rem;
	}
	
	.content-styled h4 {
		font-size: 1.125rem;
	}
}

[x-cloak] {
    display: none !important;
}

/* Custom Mobile Navigation - Drilldown Menu
--------------------------------------------- */

/* Main container must hide overflowing submenus and animate height changes */
#site-navigation {
	position: relative;
	overflow-x: hidden;
	/* Let JavaScript control/animate height dynamically */
	min-height: 0;
	height: auto;
	transition: height 0.3s ease-in-out;
}

/* All ULs are absolutely positioned layers within the container */
#site-navigation ul {
	display: block !important; /* Override default .main-navigation rule that hides ULs */
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	transition: transform 0.3s ease-in-out;
	background-color: rgb(36, 36, 36); /* Ensure background is solid */
	list-style: none;
	margin: 0;
	padding: 0;
}

/* Submenus start off-screen to the right */
#site-navigation ul.sub-menu {
	transform: translateX(100%);
}

/* The currently active menu is in view */
#site-navigation ul.is-active {
	transform: translateX(0);
	position: relative; /* Take up space for height calculation */
}

/* The parent menu is moved off-screen to the left */
#site-navigation ul.is-hidden-left {
	transform: translateX(-100%);
}

/* All links are white with a clean border */
#site-navigation a,
#site-navigation a:visited {
	color: white !important;
	display: block;
	padding: 1rem 1.5rem;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	text-transform: uppercase;
	font-weight: 500;
}

#site-navigation li:last-child > a {
	border-bottom: none;
}

/* Items with children get a caret */
#site-navigation .menu-item-has-children > a {
	position: relative;
}

#site-navigation .menu-item-has-children > a .forward-caret {
	position: absolute;
	right: 1.5rem;
	top: 50%;
	transform: translateY(-50%);
	font-size: 1.5rem;
	color: rgba(255, 255, 255, 0.5);
}

/* Back button styling */
#site-navigation .menu-back-button a {
	font-weight: bold;
	color: #ffc107 !important; /* Make back button stand out (Amber color) */
	background-color: rgba(0, 0, 0, 0.2);
	text-transform: uppercase;
}

#site-navigation .menu-back-button a::before {
	content: '‹';
	margin-right: 0.5rem;
	font-weight: bold;
}

#site-navigation ul.is-hidden-left .sub-menu {
    /* Push descendant sub-menus a full extra width so they stay off-canvas */
    transform: translateX(200%);
}
.voltax-mp-container-anchored{
	padding-bottom:100px;
}