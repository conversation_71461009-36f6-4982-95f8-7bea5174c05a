<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package LWOS_2025
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
	<script src="https://cdn.tailwindcss.com"></script>
	<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<div id="page" class="site">
	<a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e( 'Skip to content', 'lwos-2025' ); ?></a>

	<div class="hidden md:block bg-[rgb(36,36,36)] text-white font-sans text-sm">
		<div class="container mx-auto flex justify-center">
			<a href="https://lwosinc.com" class="inline-grid grid-rows-[auto_min-content] items-center gap-y-1 px-4 pt-3 pb-2 text-white visited:text-white hover:text-white group">
				<span class="uppercase font-medium">LWOS INC</span>
				<span class="h-[4px] w-full bg-transparent group-hover:bg-[rgb(255,97,97)]"></span>
			</a>
			<a href="https://lastwordonsports.com/about/" class="inline-grid grid-rows-[auto_min-content] items-center gap-y-1 px-4 pt-3 pb-2 text-white visited:text-white hover:text-white group">
				<span class="uppercase font-medium">About</span>
				<span class="h-[4px] w-full bg-transparent group-hover:bg-[rgb(255,97,97)]"></span>
			</a>
			<a href="https://lastwordonsports.com/staff-contact/" class="inline-grid grid-rows-[auto_min-content] items-center gap-y-1 px-4 pt-3 pb-2 text-white visited:text-white hover:text-white group">
				<span class="uppercase font-medium">Contact</span>
				<span class="h-[4px] w-full bg-transparent group-hover:bg-[rgb(255,97,97)]"></span>
			</a>
			<a href="https://lwosinc.com/apply/" class="inline-grid grid-rows-[auto_min-content] items-center gap-y-1 px-4 pt-3 pb-2 text-white visited:text-white hover:text-white group">
				<span class="uppercase font-medium">Apply</span>
				<span class="h-[4px] w-full bg-transparent group-hover:bg-[rgb(255,97,97)]"></span>
			</a>
			<a href="https://lwosinc.com/openings" class="inline-grid grid-rows-[auto_min-content] items-center gap-y-1 px-4 pt-3 pb-2 text-white visited:text-white hover:text-white group">
				<span class="uppercase font-medium">Openings</span>
				<span class="h-[4px] w-full bg-transparent group-hover:bg-[rgb(255,97,97)]"></span>
			</a>
			<a href="https://lwosinc.com/writing-faq" class="inline-grid grid-rows-[auto_min-content] items-center gap-y-1 px-4 pt-3 pb-2 text-white visited:text-white hover:text-white group">
				<span class="uppercase font-medium">Writing FAQ</span>
				<span class="h-[4px] w-full bg-transparent group-hover:bg-[rgb(255,97,97)]"></span>
			</a>
			<a href="https://lastwordonsports.com/ad-policy/" class="inline-grid grid-rows-[auto_min-content] items-center gap-y-1 px-4 pt-3 pb-2 text-white visited:text-white hover:text-white group">
				<span class="uppercase font-medium">Ad Policy</span>
				<span class="h-[4px] w-full bg-transparent group-hover:bg-[rgb(255,97,97)]"></span>
			</a>
		</div>
	</div>

	<header id="masthead" class="site-header font-sans text-sm">
		<?php // Desktop Header ?>
		<div class="hidden md:block bg-white shadow-md">
			<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between items-center py-6">
					<div class="site-branding flex-shrink-0 mr-8">
						<a href="https://lastwordonsports.com" rel="home">
							<img src="https://lastwordonsports.com/wp-content/uploads/sites/2/2023/04/lwos-logo-new.png" alt="<?php bloginfo( 'name' ); ?> Logo" class="h-16 w-auto">
						</a>
						<?php if ( is_front_page() && is_home() ) : ?>
							<h1 class="site-title sr-only"><a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home"><?php bloginfo( 'name' ); ?></a></h1>
						<?php else : ?>
							<p class="site-title sr-only"><a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home"><?php bloginfo( 'name' ); ?></a></p>
						<?php endif; ?>
						<?php
						$lwos_2025_description = get_bloginfo( 'description', 'display' );
						if ( $lwos_2025_description || is_customize_preview() ) :
							?>
							<p class="site-description sr-only"><?php echo $lwos_2025_description; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?></p>
						<?php endif; ?>
					</div>

					<?php // Desktop Navigation will go here ?>
					<nav id="site-navigation-desktop" class="main-navigation hidden md:flex items-center">
						<?php
						wp_nav_menu(
							array(
								'theme_location' => 'menu-1',
								'menu_id'        => 'primary-menu-desktop',
								'container'      => false,
								'items_wrap'     => '<ul id="%1$s" class="%2$s flex space-x-1 md:space-x-2 lg:space-x-4">%3$s</ul>', // Adjusted spacing
								'menu_class'     => 'primary-menu', // Simpler class, walker handles item styling
								'walker'         => new LWOS_Desktop_Nav_Walker(),
							)
						);
						?>
					</nav>

					<div class="hidden md:flex items-center justify-end ml-4 lg:ml-8">
						<button id="desktop-search-button" type="button" class="p-2 text-gray-600 hover:text-black" aria-label="Search">
							<svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
							</svg>
						</button>
					</div>
				</div>
			</div>
			<?php // Hidden Search Form for Desktop ?>
			<div id="desktop-search-form-container" class="hidden bg-white max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
				<?php get_search_form(); ?>
			</div>
		</div>

		<?php // Mobile Header ?>
		<div class="md:hidden bg-[rgb(36,36,36)] text-white">
			<div class="px-4 py-3 flex items-center justify-between">
				<div> <?php // Left item: Hamburger menu ?>
					<button id="mobile-menu-toggle-button" class="menu-toggle p-2 rounded-md text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white" aria-controls="site-navigation" aria-expanded="false">
						<span class="sr-only">Open main menu</span>
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					</button>
				</div>
				<div class="site-branding-mobile"> <?php // Center item: Logo ?>
					<a href="https://lastwordonsports.com" rel="home">
						<img src="https://lastwordonsports.com/basketball/wp-content/uploads/sites/9/2023/09/lwos-logo-new-v01.png" alt="<?php bloginfo( 'name' ); ?> Logo" class="h-8 w-auto">
					</a>
				</div>
				<div> <?php // Right item: Search Icon ?>
					<button id="mobile-search-button" type="button" class="p-2 rounded-md text-gray-400 hover:text-white focus:outline-none" aria-label="Search">
						<svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
						</svg>
					</button>
				</div>
			</div>
			<?php // Hidden Search Form for Mobile ?>
			<div id="mobile-search-form-container" class="hidden px-4 pb-4">
				<?php get_search_form(); // Will need Tailwind styling ?>
			</div>
		</div>

		<?php // Mobile Navigation Menu (original #site-navigation) ?>
		<nav id="site-navigation" class="main-navigation hidden bg-[rgb(36,36,36)] text-white">
			<?php
			wp_nav_menu(
				array(
					'theme_location' => 'menu-1',
					'menu_id'        => 'primary-menu-mobile', // Controlled by mobile-menu-toggle
					'container'      => false,
					'items_wrap'     => '<ul id="%1$s" class="%2$s block space-y-1">%3$s</ul>', // Clean mobile menu styling
					// LIs and A tags will need styling via Walker or CSS for padding, hover, etc.
				)
			);
			?>
		</nav>
	</header><!-- #masthead -->

	<?php // Orange Tagline Bar ?>
	<div class="bg-[rgb(255,136,0)] text-white py-4">
		<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
			<h2 class="text-white italic font-semibold" style="font-family: Roboto, sans-serif; font-size: 19px; font-weight: 600; height: 19px; line-height: 19px;">Sports. Honestly. Since 2011</h2>
		</div>
	</div>

<?php // Freestar Header Ad ?>
<?php // do_action( 'lwos_ad_lwos_header' ); ?>


</div>
<!-- #page will be closed in footer.php -->
