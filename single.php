<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package LWOS_2025
 */

get_header();
?>

<main id="primary" class="site-main">
	<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
			
			<?php // Main Content Area (3/4 width) ?>
			<div class="lg:col-span-3">
				<?php
				while ( have_posts() ) :
					the_post();
					?>
					
					<article id="post-<?php the_ID(); ?>" <?php post_class('bg-white rounded-lg shadow-md overflow-hidden'); ?>>
						<?php // Featured Image ?>
						<?php if ( has_post_thumbnail() ) : ?>
							<div class="w-full aspect-w-3 aspect-h-2">
								<?php the_post_thumbnail('lwos_large_3_2', array('class' => 'w-full h-full object-cover')); ?>
							</div>
						<?php endif; ?>
						
						<div class="p-6 md:p-8">
							<?php // Post Meta ?>
							<div class="flex flex-wrap items-center text-sm text-gray-500 mb-4 space-x-4">
								<span class="flex items-center">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
									</svg>
									<?php echo get_the_date('F j, Y'); ?>
								</span>
								<span class="flex items-center">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
									</svg>
									By&nbsp;<?php the_author_posts_link(); ?>
								</span>
								<?php if ( has_category() ) : ?>
									<span class="flex items-center">
										<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
										</svg>
										<?php the_category(', '); ?>
									</span>
								<?php endif; ?>
							</div>
							
							<?php // Post Title ?>
							<header class="entry-header mb-6">
								<h1 class="entry-title text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
									<?php the_title(); ?>
								</h1>
							</header>
							
							<?php // Post Content ?>
							<div class="entry-content content-styled max-w-none">
								<?php
								the_content();

								wp_link_pages(
									array(
										'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'lwos-2025' ),
										'after'  => '</div>',
									)
								);
								?>
							</div>
							
							<?php // Tags ?>
							<?php if ( has_tag() ) : ?>
								<div class="entry-footer mt-8 pt-6 border-t border-gray-200">
									<div class="flex flex-wrap items-center gap-2">
										<span class="text-sm font-medium text-gray-700">Tags:</span>
										<?php
										$tags = get_the_tags();
										if ( $tags ) {
											foreach ( $tags as $tag ) {
												echo '<a href="' . get_tag_link($tag->term_id) . '" class="inline-block bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm px-3 py-1 rounded-full transition-colors">' . $tag->name . '</a>';
											}
										}
										?>
									</div>
								</div>
							<?php endif; ?>
						</div>
					</article>

					<?php // Post Navigation ?>
					<div class="mt-8">
						<?php
						the_post_navigation(
							array(
								'prev_text' => '<div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"><span class="text-sm text-gray-500 block mb-2">' . esc_html__( 'Previous Article', 'lwos-2025' ) . '</span><span class="text-lg font-semibold text-gray-900 hover:text-blue-600">%title</span></div>',
								'next_text' => '<div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"><span class="text-sm text-gray-500 block mb-2">' . esc_html__( 'Next Article', 'lwos-2025' ) . '</span><span class="text-lg font-semibold text-gray-900 hover:text-blue-600">%title</span></div>',
								'in_same_term' => false,
								'screen_reader_text' => esc_html__( 'Continue Reading', 'lwos-2025' ),
							)
						);
						?>
					</div>

					<?php // Author Bio ?>
					<?php if ( get_the_author_meta( 'description' ) ) : ?>
						<div class="bg-gray-50 rounded-lg p-6 mt-8">
							<div class="flex items-start space-x-4">
								<div class="flex-shrink-0">
									<?php echo get_avatar( get_the_author_meta( 'ID' ), 64, '', '', array( 'class' => 'rounded-full' ) ); ?>
								</div>
								<div class="flex-1 min-w-0">
									<h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 mb-2">
										<a href="<?php echo get_author_posts_url( get_the_author_meta( 'ID' ) ); ?>">
											About <?php the_author(); ?>
										</a>
									</h3>
									<p class="text-gray-600">
										<?php the_author_meta( 'description' ); ?>
									</p>
								</div>
							</div>
						</div>
					<?php endif; ?>

					<?php
					// If comments are open or we have at least one comment, load up the comment template.
					if ( comments_open() || get_comments_number() ) :
						?>
						<div class="mt-8">
							<?php comments_template(); ?>
						</div>
						<?php
					endif;

				endwhile; // End of the loop.
				?>
			</div>
			<?php // Right Sidebar for Ads (1/4 width) ?>
			<div class="lg:col-span-1">
				<div class="sticky top-8 space-y-6">
					<?php // Ad Space 1 - Large Banner ?>
					<?php do_action( 'lwos_ad_lwos_right_rail_1' ); ?>
										
					<?php // Related Articles ?>
					<div class="bg-white rounded-lg shadow-md p-6">
						<h3 class="text-xl font-bold text-gray-900 mb-4">Related Articles</h3>
						<?php
						$current_post_id = get_queried_object_id(); // ✅ more reliable than get_the_ID()
						$category_ids = wp_get_post_categories($current_post_id);
						$tag_ids = wp_get_post_tags($current_post_id, array('fields' => 'ids'));

						$tax_query = array('relation' => 'OR');

						if (!empty($category_ids)) {
							$tax_query[] = array(
								'taxonomy' => 'category',
								'field'    => 'term_id',
								'terms'    => $category_ids,
							);
						}

						if (!empty($tag_ids)) {
							$tax_query[] = array(
								'taxonomy' => 'post_tag',
								'field'    => 'term_id',
								'terms'    => $tag_ids,
							);
						}

						$related_posts = get_posts(array(
							'post_type'      => 'post',
							'posts_per_page' => 4,
							'post__not_in'   => array($current_post_id), // ✅ exclude current post
							'orderby'        => 'date',
							'order'          => 'DESC',
							'date_query'     => array(
								array(
									'after'     => 'January 1st, 2024',
									'inclusive' => true,
								),
							),
							'tax_query'      => $tax_query,
						));

						if ($related_posts) : ?>
							<div class="space-y-4">
								<?php foreach ($related_posts as $related_post) : ?>
									<?php if ($related_post->ID === $current_post_id) continue; // fallback safeguard ?>
									<div class="group">
										<a href="<?php echo get_permalink($related_post->ID); ?>" class="block">
											<div class="flex space-x-3">
												<div class="flex-shrink-0 w-16 h-16 rounded-md overflow-hidden bg-gray-200 flex items-center justify-center">
													<?php if (has_post_thumbnail($related_post->ID)) : ?>
														<img src="<?php echo get_the_post_thumbnail_url($related_post->ID, 'lwos_medium_3_2'); ?>" alt="<?php echo esc_attr(get_the_title($related_post->ID)); ?>" class="w-full h-full object-cover" />
													<?php else : ?>
														<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm1 2h10l-5 4-5-4zm-1 2.236l5 4 5-4V15H4V7.236z"/></svg>
													<?php endif; ?>
												</div>
												<div class="flex-1 min-w-0">
													<h4 class="text-sm font-semibold text-gray-900 group-hover:text-blue-600 leading-tight line-clamp-3">
														<?php echo get_the_title($related_post->ID); ?>
													</h4>
													<p class="text-xs text-gray-500 mt-1">
														<?php echo get_the_date('M j, Y', $related_post->ID); ?>
													</p>
												</div>
											</div>
										</a>
									</div>
								<?php endforeach; ?>
							</div>
						<?php else : ?>
							<p class="text-gray-500 text-sm">No related articles found.</p>
						<?php endif;

						wp_reset_postdata();
						?>
					</div>
					
					<?php // Ad Space 2 - Medium Banner ?>
					<?php do_action( 'lwos_ad_lwos_right_rail_2' ); ?>
					
					<?php // Newsletter Signup ?>
					<div class="bg-gray-800 rounded-lg p-6 text-white">
						<h3 class="text-xl font-bold mb-4">Stay in the Game</h3>
						<p class="text-gray-300 mb-4 text-sm">Get the latest sports news and analysis delivered to your inbox.</p>
						<form class="space-y-3">
							<input type="email" placeholder="Your email" 
								   class="w-full px-3 py-2 bg-gray-700 text-white placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
							<button type="submit" 
									class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition-colors text-sm">
								Subscribe
							</button>
						</form>
					</div>
					
					<?php // Social Share ?>
					<div class="bg-white rounded-lg shadow-md p-6">
						<h3 class="text-lg font-bold text-gray-900 mb-4">Share This Article</h3>
						<div class="flex space-x-3">
							<a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" 
							   target="_blank" rel="noopener" 
							   class="flex items-center justify-center w-10 h-10 bg-blue-400 hover:bg-blue-500 text-white rounded-full transition-colors">
								<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
									<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
								</svg>
							</a>
							<a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" 
							   target="_blank" rel="noopener" 
							   class="flex items-center justify-center w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-colors">
								<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
									<path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
								</svg>
							</a>
							<a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>" 
							   target="_blank" rel="noopener" 
							   class="flex items-center justify-center w-10 h-10 bg-blue-800 hover:bg-blue-900 text-white rounded-full transition-colors">
								<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
									<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
								</svg>
							</a>
						</div>
					</div>
					
					<?php // Ad Space 3 - Small Banner ?>
					<?php do_action( 'lwos_ad_lwos_right_rail_3' ); ?>
					
				</div>
			</div>
		</div>
	</div>
</main><!-- #main -->

<?php
get_footer();
