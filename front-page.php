<?php
/**
 * The front page template file
 *
 * This is the homepage template that displays the main content feed
 * with the specific layout: featured posts, sidebar content, and call-to-action sections.
 *
 * @package LWOS_2025
 */

get_header();
?>

<main id="primary" class="site-main">
	<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		
		<?php
		// Check if we're on site ID 2 (the main LWOS site) and not other subsites
		$current_site_id = get_current_blog_id();
		$is_main_site = ($current_site_id === 2);

		// Get current page number for pagination
		$paged = get_query_var('paged') ? get_query_var('paged') : 1;
		$posts_per_page = 30; // Posts per page
		$offset = ($paged - 1) * $posts_per_page; // Calculate offset

		if ($is_main_site && is_multisite()) {
			// Get posts from all sites in the network for the main homepage
			$all_posts = lwos_get_network_posts($posts_per_page, $offset);
		} else {
			// Get posts from current site only (for subsites)
			$all_posts = get_posts(array(
				'posts_per_page' => $posts_per_page,
				'offset' => $offset,
				'post_status' => 'publish',
				'date_query'  => array(
					array(
						'after' => '2020-12-31',
						'inclusive' => true,
					),
				),
			));
		}
		
		// Split posts into sections
		$featured_posts = array_slice($all_posts, 0, 5);   // First 5 posts
		$sidebar_posts_1 = array_slice($all_posts, 5, 10); // Next 10 posts
		$cta_posts = array_slice($all_posts, 15, 5);       // Next 5 posts
		$sidebar_posts_2 = array_slice($all_posts, 20, 10); // Final 10 posts
		?>

		<?php // Section 1: Featured Posts Card (First 5 posts) ?>
		<section class="mb-12">
			<div class="bg-white rounded-lg shadow-lg overflow-hidden">
				<div class="bg-gray-800 text-white px-6 py-4">
					<h2 class="text-2xl font-bold">Latest Sports News</h2>
				</div>
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 p-6">
					<?php foreach ($featured_posts as $post) : ?>
						<div class="group">
							<?php 
							// Use the correct permalink for network posts
							$post_url = isset($post->permalink) ? $post->permalink : get_permalink($post->ID);
							?>
							<a href="<?php echo esc_url($post_url); ?>" class="block">
								<div class="aspect-w-3 aspect-h-2 mb-3">
									<?php if (isset($post->has_thumbnail) ? $post->has_thumbnail : has_post_thumbnail($post->ID)) : ?>
										<img src="<?php echo isset($post->featured_image_url) ? esc_url($post->featured_image_url) : get_the_post_thumbnail_url($post->ID, 'lwos_medium_3_2'); ?>" 
											 alt="<?php echo esc_attr($post->post_title); ?>" 
											 class="w-full h-48 object-cover rounded-md group-hover:opacity-90 transition-opacity">
									<?php else : ?>
										<div class="w-full h-48 bg-gray-200 rounded-md flex items-center justify-center">
											<span class="text-gray-400">No Image</span>
										</div>
									<?php endif; ?>
								</div>
								<h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors leading-tight">
									<?php echo esc_html($post->post_title); ?>
									<?php if (isset($post->site_name) && $is_main_site) : ?>
										<span class="block text-xs text-gray-500 font-normal mt-1">
											from <?php echo esc_html($post->site_name); ?>
										</span>
									<?php endif; ?>
								</h3>
							</a>
						</div>
					<?php endforeach; ?>
				</div>
			</div>
		</section>

		<?php // Ad Section - Ad Inserter Block 10 ?>
		<section class="mb-12">
			<div class="max-w-4xl mx-auto">
				<?php do_action( 'lwos_ad_lwos_billboard_BTF' ); ?>
			</div>
		</section>

		<?php // Section 2: Main Content with Sidebar (Next 10 posts) ?>
		<section class="mb-12">
			<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
				
				<?php // Left Content (3/4 width) ?>
				<div class="lg:col-span-3">
					<h2 class="text-2xl font-bold text-gray-900 mb-6">Recent Articles</h2>
					<div class="space-y-6">
						<?php foreach ($sidebar_posts_1 as $post) : ?>
							<article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
								<div class="md:flex">
									<div class="md:w-1/3">
										<?php 
										$post_url = isset($post->permalink) ? $post->permalink : get_permalink($post->ID);
										?>
										<?php if (isset($post->has_thumbnail) ? $post->has_thumbnail : has_post_thumbnail($post->ID)) : ?>
											<a href="<?php echo esc_url($post_url); ?>">
												<img src="<?php echo isset($post->featured_image_url) ? esc_url($post->featured_image_url) : get_the_post_thumbnail_url($post->ID, 'lwos_medium_3_2'); ?>" 
													 alt="<?php echo esc_attr($post->post_title); ?>" 
													 class="w-full h-48 md:h-full object-cover">
											</a>
										<?php else : ?>
											<div class="w-full h-48 md:h-full bg-gray-200 flex items-center justify-center">
												<span class="text-gray-400">No Image</span>
											</div>
										<?php endif; ?>
									</div>
									<div class="md:w-2/3 p-6">
										<div class="text-sm text-gray-500 mb-2">
											<?php echo isset($post->formatted_date) ? esc_html($post->formatted_date) : get_the_date('F j, Y', $post->ID); ?>
											<?php if (isset($post->site_name) && $is_main_site) : ?>
												<span class="ml-2">• from <?php echo esc_html($post->site_name); ?></span>
											<?php endif; ?>
										</div>
										<h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600">
											<a href="<?php echo esc_url($post_url); ?>">
												<?php echo esc_html($post->post_title); ?>
											</a>
										</h3>
										<p class="text-gray-600 mb-4">
											<?php 
											if (isset($post->post_excerpt_processed)) {
												echo esc_html(wp_trim_words($post->post_excerpt_processed, 20));
											} else {
												echo esc_html(wp_trim_words(get_the_excerpt($post->ID), 20));
											}
											?>
										</p>
										<a href="<?php echo esc_url($post_url); ?>" 
											 class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
											Read More
											<svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
											</svg>
										</a>
									</div>
								</div>
							</article>
						<?php endforeach; ?>
					</div>
				</div>

				<?php // Right Sidebar (1/4 width) ?>
				<div class="lg:col-span-1">
					<div class="sticky top-8 space-y-6">
						<?php // Ad Space 1 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_1' ); ?>
						
						<?php // Ad Space 2 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_2' ); ?>
					</div>
				</div>
			</div>
		</section>

		<?php // Section 3: Call to Action with 5 Posts ?>
		<section class="mb-12">
			<div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg overflow-hidden">
				<div class="px-6 py-8 text-center">
					<h2 class="text-3xl font-bold text-white mb-4">Don't Miss These Stories</h2>
					<p class="text-blue-100 mb-8">Stay updated with our most engaging content</p>
					
					<div class="grid grid-cols-1 md:grid-cols-5 gap-6">
						<?php foreach ($cta_posts as $post) : ?>
							<div class="bg-white rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all duration-300">
								<?php 
								$post_url = isset($post->permalink) ? $post->permalink : get_permalink($post->ID);
								?>
								<a href="<?php echo esc_url($post_url); ?>" class="block">
									<?php if (isset($post->has_thumbnail) ? $post->has_thumbnail : has_post_thumbnail($post->ID)) : ?>
										<img src="<?php echo isset($post->featured_image_url) ? esc_url($post->featured_image_url) : get_the_post_thumbnail_url($post->ID, 'lwos_medium_3_2'); ?>" 
											 alt="<?php echo esc_attr($post->post_title); ?>" 
											 class="w-full h-32 object-cover">
									<?php else : ?>
										<div class="w-full h-32 bg-gray-200 flex items-center justify-center">
											<span class="text-gray-400 text-sm">No Image</span>
										</div>
									<?php endif; ?>
									<div class="p-4">
										<h3 class="text-sm font-semibold text-gray-900 leading-tight">
											<?php echo esc_html(wp_trim_words($post->post_title, 8)); ?>
										</h3>
										<?php if (isset($post->site_name) && $is_main_site) : ?>
											<span class="block text-xs text-gray-500 mt-1">
												from <?php echo esc_html($post->site_name); ?>
											</span>
										<?php endif; ?>
									</div>
								</a>
							</div>
						<?php endforeach; ?>
					</div>
				</div>
			</div>
		</section>

		<?php // Section 4: More Articles with Sidebar (Final 10 posts) ?>
		<section class="mb-12">
			<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
				
				<?php // Left Content (3/4 width) ?>
				<div class="lg:col-span-3">
					<h2 class="text-2xl font-bold text-gray-900 mb-6">More Stories</h2>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
						<?php foreach ($sidebar_posts_2 as $post) : ?>
							<article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
								<?php 
								$post_url = isset($post->permalink) ? $post->permalink : get_permalink($post->ID);
								?>
								<?php if (isset($post->has_thumbnail) ? $post->has_thumbnail : has_post_thumbnail($post->ID)) : ?>
									<a href="<?php echo esc_url($post_url); ?>">
										<img src="<?php echo isset($post->featured_image_url) ? esc_url($post->featured_image_url) : get_the_post_thumbnail_url($post->ID, 'lwos_medium_3_2'); ?>" 
											 alt="<?php echo esc_attr($post->post_title); ?>" 
											 class="w-full h-48 object-cover">
									</a>
								<?php else : ?>
									<div class="w-full h-48 bg-gray-200 flex items-center justify-center">
										<span class="text-gray-400">No Image</span>
									</div>
								<?php endif; ?>
								<div class="p-6">
									<div class="text-sm text-gray-500 mb-2">
										<?php echo isset($post->formatted_date) ? esc_html($post->formatted_date) : get_the_date('F j, Y', $post->ID); ?>
										<?php if (isset($post->site_name) && $is_main_site) : ?>
											<span class="ml-2">• from <?php echo esc_html($post->site_name); ?></span>
										<?php endif; ?>
									</div>
									<h3 class="text-lg font-bold text-gray-900 mb-3 hover:text-blue-600">
										<a href="<?php echo esc_url($post_url); ?>">
											<?php echo esc_html($post->post_title); ?>
										</a>
									</h3>
									<p class="text-gray-600 mb-4">
										<?php 
										if (isset($post->post_excerpt_processed)) {
											echo esc_html(wp_trim_words($post->post_excerpt_processed, 15));
										} else {
											echo esc_html(wp_trim_words(get_the_excerpt($post->ID), 15));
										}
										?>
									</p>
									<a href="<?php echo esc_url($post_url); ?>" 
										 class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm">
										Read More
									</a>
								</div>
							</article>
						<?php endforeach; ?>
					</div>
				</div>

				<?php // Right Sidebar (1/4 width) ?>
				<div class="lg:col-span-1">
					<div class="sticky top-8 space-y-6">
						<?php // Newsletter Signup ?>
						<div class="bg-gray-800 rounded-lg p-6 text-white">
							<h3 class="text-xl font-bold mb-4">Stay Updated</h3>
							<p class="text-gray-300 mb-4">Get the latest sports news delivered to your inbox.</p>
							<form class="space-y-3">
								<input type="email" placeholder="Your email" 
									   class="w-full px-3 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
								<button type="submit" 
										class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition-colors">
									Subscribe
								</button>
							</form>
						</div>
						
						<?php // Social Links ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-xl font-bold text-gray-900 mb-4">Follow Us</h3>
							<div class="space-y-3">
								<a href="#" class="flex items-center text-gray-600 hover:text-blue-600 transition-colors">
									<svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
										<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
									</svg>
									Twitter
								</a>
								<a href="#" class="flex items-center text-gray-600 hover:text-blue-600 transition-colors">
									<svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
										<path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
									</svg>
									Facebook
								</a>
								<a href="#" class="flex items-center text-gray-600 hover:text-blue-600 transition-colors">
									<svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
										<path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.024-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.347-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.758-1.378l-.749 2.848c-.269 1.045-1.004 2.352-1.498 3.146 1.123.345 2.306.535 3.55.535 6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
									</svg>
									Instagram
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<?php // Pagination Section ?>
		<section class="text-center">
			<div class="bg-white rounded-lg shadow-md p-8">
				<?php
				// Only show pagination if we have posts
				if (!empty($all_posts)) :
					$next_page = $paged + 1;
					$prev_page = $paged - 1;
					$has_next = count($all_posts) >= $posts_per_page; // If we got full page, assume there might be more
					$has_prev = $paged > 1;
				?>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">
						<?php if ($paged > 1) : ?>
							Page <?php echo $paged; ?> - More Stories
						<?php else : ?>
							Load More Content
						<?php endif; ?>
					</h3>
					<p class="text-gray-600 mb-6">Discover more stories and stay updated with the latest sports news.</p>

					<div class="flex justify-center items-center space-x-4">
						<?php if ($has_prev) : ?>
							<a href="<?php echo $prev_page > 1 ? get_pagenum_link($prev_page) : home_url('/'); ?>"
							   class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white hover:text-orange-500 font-semibold py-3 px-6 rounded-lg transition-colors">
								<svg class="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"></path>
								</svg>
								Previous Page
							</a>
						<?php endif; ?>

						<?php if ($has_next) : ?>
							<a href="<?php echo get_pagenum_link($next_page); ?>"
							   class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white hover:text-orange-500 font-semibold py-3 px-6 rounded-lg transition-colors">
								Next Page
								<svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
								</svg>
							</a>
						<?php endif; ?>
					</div>

					<?php if ($paged > 1) : ?>
						<div class="mt-4">
							<a href="<?php echo home_url('/'); ?>" class="text-blue-600 hover:text-blue-800 font-medium">
								← Back to Homepage
							</a>
						</div>
					<?php endif; ?>
				<?php endif; ?>
			</div>
		</section>

	</div>
</main><!-- #main -->

<?php
get_footer(); 