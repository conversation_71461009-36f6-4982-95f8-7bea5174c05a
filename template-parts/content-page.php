<?php
/**
 * Template part for displaying page content in page.php
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package LWOS_2025
 */

?>

<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<article id="post-<?php the_ID(); ?>" <?php post_class('bg-white rounded-lg shadow-md overflow-hidden max-w-4xl mx-auto'); ?>>
		
		<?php lwos_2025_post_thumbnail(); ?>
		
		<div class="p-6 md:p-8">
			<header class="entry-header mb-6">
				<?php the_title( '<h1 class="entry-title text-3xl md:text-4xl font-bold text-gray-900 leading-tight">', '</h1>' ); ?>
			</header><!-- .entry-header -->

			<div class="entry-content content-styled max-w-none">
				<?php
				the_content();

				wp_link_pages(
					array(
						'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'lwos-2025' ),
						'after'  => '</div>',
					)
				);
				?>
			</div><!-- .entry-content -->

			<?php if ( get_edit_post_link() ) : ?>
				<footer class="entry-footer mt-8 pt-6 border-t border-gray-200">
					<?php
					edit_post_link(
						sprintf(
							wp_kses(
								/* translators: %s: Name of current post. Only visible to screen readers */
								__( 'Edit <span class="screen-reader-text">%s</span>', 'lwos-2025' ),
								array(
									'span' => array(
										'class' => array(),
									),
								)
							),
							wp_kses_post( get_the_title() )
						),
						'<span class="edit-link inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors">',
						'</span>'
					);
					?>
				</footer><!-- .entry-footer -->
			<?php endif; ?>
		</div>
	</article><!-- #post-<?php the_ID(); ?> -->
</div>
