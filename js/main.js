jQuery(function ($) {
    console.log('Main.js loaded (jQuery version)'); // Log: Script loaded

    /* --------------------------------------------------
     * Desktop Search Toggle
     * -------------------------------------------------- */
    var $desktopSearchButton = $('#desktop-search-button');
    var $desktopSearchFormContainer = $('#desktop-search-form-container');

    $desktopSearchButton.on('click', function (e) {
        e.preventDefault();
        $desktopSearchFormContainer.toggleClass('hidden');

        var desktopVisible = !$desktopSearchFormContainer.hasClass('hidden');
        console.log('Desktop search toggled. Visible:', desktopVisible);

        if (desktopVisible) {
            $desktopSearchFormContainer.find('input[type="search"]').first().focus();
        }
    });

    /* --------------------------------------------------
     * Mobile Search Toggle
     * -------------------------------------------------- */
    var $mobileSearchButton = $('#mobile-search-button');
    var $mobileSearchFormContainer = $('#mobile-search-form-container');

    $mobileSearchButton.on('click', function (e) {
        e.preventDefault();
        $mobileSearchFormContainer.toggleClass('hidden');

        var mobileSearchVisible = !$mobileSearchFormContainer.hasClass('hidden');
        console.log('Mobile search toggled. Visible:', mobileSearchVisible);

        if (mobileSearchVisible) {
            $mobileSearchFormContainer.find('input[type="search"]').first().focus();
        }
    });

    /* --------------------------------------------------
     * Mobile Menu Drilldown Logic
     * -------------------------------------------------- */
    const $navContainer = $('#site-navigation');
    const $mobileMenuButton = $('#mobile-menu-toggle-button');

    // In our markup wp_nav_menu outputs the UL directly inside #site-navigation (no wrapper DIV)
    const $rootUl = $navContainer.children('ul');

    // Utility: Update container height to match current menu
    function updateNavHeight() {
        const $activeMenu = $navContainer.children('ul.is-active').first();
        if ($activeMenu.length) {
            $navContainer.height($activeMenu.outerHeight());
        }
    }

    // Bail if we didn't find a root <ul>
    if (!$rootUl.length) {
        return;
    }

    // --- 1. Initial Setup: Augment the menu markup ---
    $rootUl.addClass('is-active').attr('data-level', 0);

    $navContainer.find('.menu-item-has-children').each(function() {
        const $parentLi = $(this);
        const $parentLink = $parentLi.children('a');
        const $subMenu = $parentLi.children('.sub-menu');
        
        // Add a forward caret to the parent link
        $parentLink.append('<span class="forward-caret" aria-hidden="true">›</span>');
        
        // Add a back button to the submenu
        const backButtonText = $parentLink.clone().children().remove().end().text(); // Get text without child spans
        const backButton = $('<li class="menu-back-button"><a href="#" role="button">' + backButtonText + '</a></li>');
        $subMenu.prepend(backButton);
        $subMenu.data('lwosParentLi', $parentLi); // store reference for back action
    });


    // --- 2. Click Handlers ---

    // Open/Close the main navigation container
    $mobileMenuButton.on('click', function(e) {
        e.preventDefault();
        
        $navContainer.toggleClass('hidden');
        const isVisible = !$navContainer.hasClass('hidden');
        $(this).attr('aria-expanded', isVisible.toString());

        if (isVisible) {
            // Ensure height fits the root menu when opening
            updateNavHeight();
        } else {
            // Reset the menu state when closing
            setTimeout(function() {
                $navContainer.find('.is-hidden-left').removeClass('is-hidden-left');
                $navContainer.find('.sub-menu.is-active').removeClass('is-active');
                $rootUl.addClass('is-active');
                // Clear the explicit height so it can collapse
                $navContainer.height('');
            }, 300);
        }
    });

    // Handle clicking a link that opens a submenu
    $navContainer.on('click', '.menu-item-has-children > a', function(e) {
        if ($(e.target).closest('.menu-back-button').length) {
            return;
        }
        e.preventDefault();
        
        const $parentLi = $(this).parent('li');
        const $currentMenu = $parentLi.parent('ul');
        const $nextMenu = $parentLi.children('.sub-menu');

        // Before showing the next submenu, move any other submenus (previously appended) back to their original <li>
        $navContainer.children('ul.sub-menu').not($currentMenu).not($nextMenu).each(function(){
            const $sm = $(this);
            const $smParentLi = $sm.data('lwosParentLi');
            if ($smParentLi && $smParentLi.length) {
                $sm.detach().appendTo($smParentLi);
                $sm.removeClass('is-active is-hidden-left');
            }
        });

        // Remove active state from any other menu before transitioning
        $navContainer.find('ul.is-active').not($currentMenu).not($nextMenu).removeClass('is-active');

        // Move the submenu we are opening to be a direct child of navContainer so it's not affected by parent transforms
        $nextMenu.appendTo($navContainer);

        // Hide the current menu and activate the next
        $currentMenu.addClass('is-hidden-left').removeClass('is-active');
        $nextMenu.addClass('is-active');

        // Adjust height for the newly active submenu
        updateNavHeight();
    });

    // Handle clicking the back button
    $navContainer.on('click', '.menu-back-button > a', function(e) {
        e.preventDefault();
        
        const $currentMenu = $(this).closest('.sub-menu');
        const $parentLi = $currentMenu.data('lwosParentLi');

        if (!$parentLi || !$parentLi.length) {
            return; // safety guard
        }

        const $parentMenu = $parentLi.parent('ul');

        // Move submenu back under its parent <li>
        $currentMenu.detach().appendTo($parentLi);

        // Restore class states
        $currentMenu.removeClass('is-active');
        $parentMenu.removeClass('is-hidden-left').addClass('is-active');

        // Adjust height for the parent menu
        updateNavHeight();
    });
}); 