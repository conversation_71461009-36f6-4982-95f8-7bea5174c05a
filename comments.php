<?php
/**
 * The template for displaying comments
 *
 * This is the template that displays the area of the page that contains both the current comments
 * and the comment form.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package LWOS_2025
 */

/*
 * If the current post is protected by a password and
 * the visitor has not yet entered the password we will
 * return early without loading the comments.
 */
if ( post_password_required() ) {
	return;
}
?>

<div id="comments" class="comments-area bg-white rounded-lg shadow-md overflow-hidden mt-8">
	
	<?php if ( have_comments() ) : ?>
		
		<?php // Comments Header ?>
		<div class="bg-gradient-to-r from-[rgb(36,36,36)] to-gray-700 px-6 py-8">
			<div class="flex items-center justify-between">
				<div>
					<h2 class="comments-title text-2xl md:text-3xl font-bold text-white mb-2">
						<?php
						$lwos_2025_comment_count = get_comments_number();
						if ( '1' === $lwos_2025_comment_count ) {
							echo '<span class="flex items-center"><svg class="w-6 h-6 mr-3 text-[rgb(255,136,0)]" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>1 Comment</span>';
						} else {
							echo '<span class="flex items-center"><svg class="w-6 h-6 mr-3 text-[rgb(255,136,0)]" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' . number_format_i18n( $lwos_2025_comment_count ) . ' Comments</span>';
						}
						?>
					</h2>
					<p class="text-gray-300 text-sm">Join the conversation about this article</p>
				</div>
				<div class="hidden md:block">
					<div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
						<svg class="w-8 h-8 text-[rgb(255,136,0)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-1"></path>
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12V8a2 2 0 012-2h6a2 2 0 012 2v4a2 2 0 01-2 2H9z"></path>
						</svg>
					</div>
				</div>
			</div>
		</div>

		<?php // Comments Navigation ?>
		<div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
			<?php
			the_comments_navigation(array(
				'prev_text' => '<span class="inline-flex items-center text-gray-600 hover:text-[rgb(255,136,0)] transition-colors"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>Older Comments</span>',
				'next_text' => '<span class="inline-flex items-center text-gray-600 hover:text-[rgb(255,136,0)] transition-colors">Newer Comments<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></span>',
				'screen_reader_text' => 'Comments navigation',
			));
			?>
		</div>

		<?php // Comments List ?>
		<div class="px-6 py-6">
			<ol class="comment-list space-y-6">
				<?php
				wp_list_comments(array(
					'style'       => 'ol',
					'short_ping'  => true,
					'avatar_size' => 64,
					'callback'    => 'lwos_2025_comment_callback',
				));
				?>
			</ol>
		</div>

		<?php // Bottom Comments Navigation ?>
		<div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
			<?php
			the_comments_navigation(array(
				'prev_text' => '<span class="inline-flex items-center text-gray-600 hover:text-[rgb(255,136,0)] transition-colors"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>Older Comments</span>',
				'next_text' => '<span class="inline-flex items-center text-gray-600 hover:text-[rgb(255,136,0)] transition-colors">Newer Comments<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></span>',
				'screen_reader_text' => 'Comments navigation',
			));
			?>
		</div>

		<?php
		// If comments are closed and there are comments, let's leave a little note
		if ( ! comments_open() ) :
			?>
			<div class="px-6 py-4 bg-yellow-50 border-l-4 border-yellow-400">
				<div class="flex">
					<div class="flex-shrink-0">
						<svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
						</svg>
					</div>
					<div class="ml-3">
						<p class="text-sm text-yellow-700 font-medium">
							<?php esc_html_e( 'Comments are closed for this article.', 'lwos-2025' ); ?>
						</p>
					</div>
				</div>
			</div>
			<?php
		endif;

	endif; // Check for have_comments().
	?>

	<?php if ( comments_open() ) : ?>
		<?php // Comment Form Section ?>
		<div class="bg-gray-50 px-6 py-8 border-t border-gray-200">
			<div class="mb-6">
				<h3 class="text-xl font-bold text-gray-900 mb-2 flex items-center">
					<span class="w-8 h-8 bg-[rgb(255,136,0)] rounded-full flex items-center justify-center mr-3">
						<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
						</svg>
					</span>
					Leave a Comment
				</h3>
				<p class="text-gray-600 text-sm">Share your thoughts and join the discussion</p>
			</div>
			
			<?php
			comment_form(array(
				'title_reply'          => '',
				'title_reply_to'       => 'Reply to %s',
				'title_reply_before'   => '<h3 id="reply-title" class="comment-reply-title text-lg font-semibold text-gray-900 mb-4">',
				'title_reply_after'    => '</h3>',
				'cancel_reply_before'  => '<small class="ml-2">',
				'cancel_reply_after'   => '</small>',
				'cancel_reply_link'    => '<span class="text-[rgb(255,136,0)] hover:text-orange-600 transition-colors">Cancel Reply</span>',
				'label_submit'         => 'Post Comment',
				'submit_button'        => '<button type="submit" id="%2$s" class="%3$s inline-flex items-center px-6 py-3 bg-[rgb(255,136,0)] hover:bg-orange-600 text-white font-semibold rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2">%4$s<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path></svg></button>',
				'submit_field'         => '<div class="form-submit flex justify-end mt-6">%1$s %2$s</div>',
				'comment_field'        => '<div class="mb-6"><label for="comment" class="block text-sm font-medium text-gray-700 mb-2">Comment *</label><textarea id="comment" name="comment" rows="6" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[rgb(255,136,0)] focus:border-transparent resize-none" placeholder="Share your thoughts..." required></textarea></div>',
				'fields'               => array(
					'author' => '<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"><div><label for="author" class="block text-sm font-medium text-gray-700 mb-2">Name *</label><input id="author" name="author" type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[rgb(255,136,0)] focus:border-transparent" placeholder="Your name" required></div>',
					'email'  => '<div><label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label><input id="email" name="email" type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[rgb(255,136,0)] focus:border-transparent" placeholder="<EMAIL>" required></div></div>',
					'url'    => '<div class="mb-4"><label for="url" class="block text-sm font-medium text-gray-700 mb-2">Website</label><input id="url" name="url" type="url" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[rgb(255,136,0)] focus:border-transparent" placeholder="https://yourwebsite.com"></div>',
				),
				'class_form'           => 'comment-form',
				'class_submit'         => 'submit',
			));
			?>
		</div>
	<?php endif; ?>

</div><!-- #comments -->
