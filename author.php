<?php
/**
 * The template for displaying author archive pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package LWOS_2025
 */

get_header();
?>

<main id="primary" class="site-main">
	<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		
		<?php if ( have_posts() ) : ?>
			<?php
			// Get the author information
			$author_id = get_query_var('author');
			$author = get_userdata($author_id);
			$author_posts_count = count_user_posts($author_id);
			?>
			
			<?php // Author Header Section ?>
			<div class="bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg shadow-lg overflow-hidden mb-12">
				<div class="px-6 py-12 md:px-12">
					<div class="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
						
						<?php // Author Avatar ?>
						<div class="flex-shrink-0">
							<div class="w-32 h-32 md:w-40 md:h-40 rounded-full overflow-hidden ring-4 ring-white shadow-lg">
								<?php echo get_avatar($author_id, 160, '', $author->display_name, array('class' => 'w-full h-full object-cover')); ?>
							</div>
						</div>
						
						<?php // Author Info ?>
						<div class="flex-1 text-center md:text-left">
							<h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
								<?php echo esc_html($author->display_name); ?>
							</h1>
							
							<?php if ( $author->description ) : ?>
								<p class="text-xl text-gray-300 mb-6 leading-relaxed">
									<?php echo wp_kses_post($author->description); ?>
								</p>
							<?php endif; ?>
							
							<div class="flex flex-col sm:flex-row items-center justify-center md:justify-start space-y-4 sm:space-y-0 sm:space-x-6 text-gray-300">
								<div class="flex items-center">
									<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
									</svg>
									<span class="font-semibold"><?php echo $author_posts_count; ?> Articles</span>
								</div>
								
								<?php if ( $author->user_email ) : ?>
									<div class="flex items-center">
										<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
										</svg>
										<a href="mailto:<?php echo esc_attr($author->user_email); ?>" class="hover:text-white transition-colors">
											Contact Author
										</a>
									</div>
								<?php endif; ?>
								
								<div class="flex items-center">
									<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
									</svg>
									<span>Joined <?php echo date('F Y', strtotime($author->user_registered)); ?></span>
								</div>
							</div>
							
							<?php // Social Links ?>
							<div class="flex justify-center md:justify-start space-x-4 mt-6">
								<?php
								$twitter = get_user_meta($author_id, 'twitter', true);
								$facebook = get_user_meta($author_id, 'facebook', true);
								$linkedin = get_user_meta($author_id, 'linkedin', true);
								$website = $author->user_url;
								
								if ($website) : ?>
									<a href="<?php echo esc_url($website); ?>" target="_blank" rel="noopener" 
									   class="flex items-center justify-center w-10 h-10 bg-white/20 hover:bg-white/30 text-white rounded-full transition-colors">
										<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
										</svg>
									</a>
								<?php endif;
								
								if ($twitter) : ?>
									<a href="<?php echo esc_url($twitter); ?>" target="_blank" rel="noopener" 
									   class="flex items-center justify-center w-10 h-10 bg-blue-400/20 hover:bg-blue-400/30 text-white rounded-full transition-colors">
										<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
											<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
										</svg>
									</a>
								<?php endif;
								
								if ($facebook) : ?>
									<a href="<?php echo esc_url($facebook); ?>" target="_blank" rel="noopener" 
									   class="flex items-center justify-center w-10 h-10 bg-blue-600/20 hover:bg-blue-600/30 text-white rounded-full transition-colors">
										<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
											<path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
										</svg>
									</a>
								<?php endif;
								
								if ($linkedin) : ?>
									<a href="<?php echo esc_url($linkedin); ?>" target="_blank" rel="noopener" 
									   class="flex items-center justify-center w-10 h-10 bg-blue-800/20 hover:bg-blue-800/30 text-white rounded-full transition-colors">
										<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
											<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
										</svg>
									</a>
								<?php endif; ?>
							</div>
						</div>
					</div>
				</div>
			</div>

			<?php // Main Content Layout ?>
			<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
				
				<?php // Author's Posts (3/4 width) ?>
				<div class="lg:col-span-3">
					<div class="flex items-center justify-between mb-8">
						<h2 class="text-3xl font-bold text-gray-900">
							Articles by <?php echo esc_html($author->display_name); ?>
						</h2>
						<div class="text-sm text-gray-500">
							<?php echo $author_posts_count; ?> articles found
						</div>
					</div>
					
					<?php // Posts Grid ?>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
						<?php while ( have_posts() ) : the_post(); ?>
							<article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
								<?php if ( has_post_thumbnail() ) : ?>
									<div class="aspect-w-3 aspect-h-2 overflow-hidden">
										<a href="<?php the_permalink(); ?>">
											<?php the_post_thumbnail('lwos_large_3_2', array('class' => 'w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300')); ?>
										</a>
									</div>
								<?php endif; ?>
								
								<div class="p-6">
									<div class="flex items-center text-sm text-gray-500 mb-3">
										<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
										</svg>
										<?php echo get_the_date('F j, Y'); ?>
										
										<?php if ( has_category() ) : ?>
											<span class="mx-2">•</span>
											<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
											</svg>
											<?php the_category(', '); ?>
										<?php endif; ?>
									</div>
									
									<h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
										<a href="<?php the_permalink(); ?>">
											<?php the_title(); ?>
										</a>
									</h3>
									
									<p class="text-gray-600 mb-4 leading-relaxed">
										<?php echo wp_trim_words(get_the_excerpt(), 20); ?>
									</p>
									
									<div class="flex items-center justify-between">
										<a href="<?php the_permalink(); ?>" 
										   class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors">
											Read More
											<svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
											</svg>
										</a>
										
										<div class="flex items-center text-sm text-gray-500">
											<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
											</svg>
											<?php comments_number('0', '1', '%'); ?>
										</div>
									</div>
								</div>
							</article>
						<?php endwhile; ?>
					</div>
					
					<?php // Pagination ?>
					<div class="mt-12">
						<?php
						the_posts_pagination(array(
							'prev_text' => '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>Previous',
							'next_text' => 'Next<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>',
							'before_page_number' => '<span class="px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 transition-colors">',
							'after_page_number' => '</span>',
						));
						?>
					</div>
				</div>

				<?php // Right Sidebar (1/4 width) ?>
				<div class="lg:col-span-1">
					<div class="sticky top-8 space-y-6">
						
						<?php // Author Stats ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Author Stats</h3>
							<div class="space-y-3">
								<div class="flex items-center justify-between">
									<span class="text-gray-600">Total Articles</span>
									<span class="font-semibold text-gray-900"><?php echo $author_posts_count; ?></span>
								</div>
								<div class="flex items-center justify-between">
									<span class="text-gray-600">Member Since</span>
									<span class="font-semibold text-gray-900"><?php echo date('M Y', strtotime($author->user_registered)); ?></span>
								</div>
								<?php
								// Get most recent post date
								$recent_posts = get_posts(array(
									'author' => $author_id,
									'numberposts' => 1,
									'post_status' => 'publish'
								));
								if ($recent_posts) :
								?>
									<div class="flex items-center justify-between">
										<span class="text-gray-600">Last Article</span>
										<span class="font-semibold text-gray-900"><?php echo get_the_date('M j', $recent_posts[0]->ID); ?></span>
									</div>
								<?php endif; wp_reset_postdata(); ?>
							</div>
						</div>
						
						<?php // Ad Space 1 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_1' ); ?>
						
						<?php // Popular Categories by this Author ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Categories</h3>
							<?php
							$author_categories = get_terms(array(
								'taxonomy' => 'category',
								'hide_empty' => true,
								'meta_query' => array(
									array(
										'key' => 'author',
										'value' => $author_id,
										'compare' => '='
									)
								)
							));
							
							if (!empty($author_categories) && !is_wp_error($author_categories)) :
							?>
								<div class="flex flex-wrap gap-2">
									<?php foreach ($author_categories as $category) : ?>
										<a href="<?php echo get_category_link($category->term_id); ?>" 
										   class="inline-block bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm px-3 py-1 rounded-full transition-colors">
											<?php echo esc_html($category->name); ?>
										</a>
									<?php endforeach; ?>
								</div>
							<?php else : ?>
								<p class="text-gray-500 text-sm">No categories found.</p>
							<?php endif; ?>
						</div>
						
						<?php // Newsletter Signup ?>
						<div class="bg-gray-800 rounded-lg p-6 text-white">
							<h3 class="text-lg font-bold mb-4">Follow <?php echo esc_html($author->display_name); ?></h3>
							<p class="text-gray-300 mb-4 text-sm">Get notified when <?php echo esc_html($author->first_name ?: $author->display_name); ?> publishes new articles.</p>
							<form class="space-y-3">
								<input type="email" placeholder="Your email" 
									   class="w-full px-3 py-2 bg-gray-700 text-white placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
								<button type="submit" 
										class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition-colors text-sm">
									Subscribe
								</button>
							</form>
						</div>
						
						<?php // Ad Space 2 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_2' ); ?>
						
					</div>
				</div>
			</div>

		<?php else : ?>
			
			<?php // No Posts Found ?>
			<div class="text-center py-16">
				<div class="max-w-md mx-auto">
					<svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.901-6.063 2.379C5.482 17.56 5.99 18 6.5 18h11c.51 0 1.018-.44.563-1.621z"></path>
					</svg>
					<h2 class="text-2xl font-bold text-gray-900 mb-2">No Articles Found</h2>
					<p class="text-gray-600">This author hasn't published any articles yet.</p>
				</div>
			</div>
			
		<?php endif; ?>
		
	</div>
</main><!-- #main -->

<?php
get_footer(); 