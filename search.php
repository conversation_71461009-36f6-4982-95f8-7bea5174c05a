<?php
/**
 * The template for displaying search results pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#search-result
 *
 * @package LWOS_2025
 */

get_header();
?>

<main id="primary" class="site-main">
	<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		
		<?php if ( have_posts() ) : ?>
			<?php
			// Get search information
			$search_query = get_search_query();
			global $wp_query;
			$total_posts = $wp_query->found_posts;
			$paged = get_query_var('paged') ? get_query_var('paged') : 1;
			?>
			
			<?php // Search Header Section ?>
			<div class="bg-gradient-to-r from-green-600 to-green-800 rounded-lg shadow-lg overflow-hidden mb-12">
				<div class="px-6 py-12 md:px-12">
					<div class="text-center">
						<div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
							</svg>
						</div>
						
						<h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
							Search Results
						</h1>
						
						<div class="text-xl text-green-100 mb-6 leading-relaxed max-w-3xl mx-auto">
							<p>Found <span class="font-semibold text-white"><?php echo $total_posts; ?></span> 
							<?php echo ($total_posts == 1) ? 'result' : 'results'; ?> for 
							<span class="font-semibold text-white">"<?php echo esc_html($search_query); ?>"</span></p>
						</div>
						
						<div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-green-100">
							<div class="flex items-center">
								<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.901-6.063 2.379C5.482 17.56 5.99 18 6.5 18h11c.51 0 1.018-.44.563-1.621z"></path>
								</svg>
								<span class="font-semibold"><?php echo $total_posts; ?> <?php echo ($total_posts == 1) ? 'Article' : 'Articles'; ?></span>
							</div>
							
							<div class="flex items-center">
								<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								<span>Search performed in <?php echo timer_stop(0, 3); ?>s</span>
							</div>
							
							<div class="flex items-center">
								<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
								</svg>
								<span>All Categories</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<?php // Ad Section - Ad Inserter Block 10 ?>
			<section class="mb-12">
            <div class="max-w-4xl mx-auto">
                <!-- Desktop Player -->
                <div id="lwos_stn_player" class="hidden md:block">
                    <div id="FreeStarVideoAdContainer">
                        <div id="freestar-video-parent">
                            <div id="freestar-video-child"></div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Player -->
                <div id="lwos_stn_player_mobile" class="block md:hidden">
                    <div id="FreeStarVideoAdContainer">
                        <div id="freestar-video-parent">
                            <div id="freestar-video-child"></div>
                        </div>
                    </div>
                </div>
            </div>
			</section>

			<?php // Main Content Layout ?>
			<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
				
				<?php // Search Results (3/4 width) ?>
				<div class="lg:col-span-3">
					<div class="flex items-center justify-between mb-8">
						<h2 class="text-3xl font-bold text-gray-900">
							Search Results
						</h2>
						<div class="text-sm text-gray-500">
							<?php
							$posts_per_page = get_query_var('posts_per_page');
							$start = ($paged - 1) * $posts_per_page + 1;
							$end = min($start + $posts_per_page - 1, $total_posts);
							
							echo "Showing {$start}-{$end} of {$total_posts} results";
							?>
						</div>
					</div>
					
					<?php // Search Results Grid ?>
					<div class="space-y-8">
						<?php while ( have_posts() ) : the_post(); ?>
							<article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
								<div class="md:flex">
									<div class="md:w-1/3">
										<?php if ( has_post_thumbnail() ) : ?>
											<a href="<?php the_permalink(); ?>">
												<?php the_post_thumbnail('medium_large', array('class' => 'w-full h-48 md:h-full object-cover group-hover:scale-105 transition-transform duration-300')); ?>
											</a>
										<?php else : ?>
											<div class="w-full h-48 md:h-full bg-gray-200 flex items-center justify-center">
												<svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
												</svg>
											</div>
										<?php endif; ?>
									</div>
									
									<div class="md:w-2/3 p-6">
										<div class="flex items-center text-sm text-gray-500 mb-3 space-x-4">
											<span class="flex items-center">
												<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
												</svg>
												<?php echo get_the_date('F j, Y'); ?>
											</span>
											<span class="flex items-center">
												<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
												</svg>
												By&nbsp;<?php the_author_posts_link(); ?>
											</span>
											<span class="flex items-center">
												<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
												</svg>
												<?php 
												$categories = get_the_category();
												if ( ! empty( $categories ) ) {
													echo '<a href="' . esc_url( get_category_link( $categories[0]->term_id ) ) . '" class="hover:text-blue-600 transition-colors">' . esc_html( $categories[0]->name ) . '</a>';
												}
												?>
											</span>
											<span class="flex items-center">
												<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
												</svg>
												<?php comments_number('0', '1', '%'); ?>
											</span>
										</div>
										
										<h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
											<a href="<?php the_permalink(); ?>">
												<?php the_title(); ?>
											</a>
										</h3>
										
										<p class="text-gray-600 mb-4 leading-relaxed">
											<?php echo wp_trim_words(get_the_excerpt(), 25); ?>
										</p>
										
										<div class="flex items-center justify-between">
											<a href="<?php the_permalink(); ?>" 
											   class="inline-flex items-center text-green-600 hover:text-green-800 font-medium transition-colors">
												Read Full Article
												<svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
												</svg>
											</a>
											
											<?php if ( has_tag() ) : ?>
												<div class="flex flex-wrap gap-1">
													<?php
													$tags = get_the_tags();
													if ( $tags ) {
														$tag_count = 0;
														foreach ( $tags as $tag ) {
															if ( $tag_count >= 2 ) break; // Limit to 2 tags
															echo '<span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">' . $tag->name . '</span>';
															$tag_count++;
														}
													}
													?>
												</div>
											<?php endif; ?>
										</div>
									</div>
								</div>
							</article>
						<?php endwhile; ?>
					</div>
					
					<?php // Pagination ?>
					<div class="mt-12">
						<?php
						the_posts_pagination(array(
							'prev_text' => '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>Previous',
							'next_text' => 'Next<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>',
							'before_page_number' => '<span class="px-4 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 transition-colors">',
							'after_page_number' => '</span>',
						));
						?>
					</div>
				</div>

				<?php // Right Sidebar (1/4 width) ?>
				<div class="lg:col-span-1">
					<div class="sticky top-8 space-y-6">
						
						<?php // Search Stats ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Search Stats</h3>
							<div class="space-y-3">
								<div class="flex items-center justify-between">
									<span class="text-gray-600">Total Results</span>
									<span class="font-semibold text-gray-900"><?php echo $total_posts; ?></span>
								</div>
								<div class="flex items-center justify-between">
									<span class="text-gray-600">Search Query</span>
									<span class="font-semibold text-gray-900 text-right text-sm max-w-24 truncate" title="<?php echo esc_attr($search_query); ?>">
										"<?php echo esc_html($search_query); ?>"
									</span>
								</div>
								<div class="flex items-center justify-between">
									<span class="text-gray-600">Search Time</span>
									<span class="font-semibold text-gray-900"><?php echo timer_stop(0, 3); ?>s</span>
								</div>
							</div>
						</div>
						
						<?php // Refined Search ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Refine Your Search</h3>
							<form role="search" method="get" action="<?php echo esc_url( home_url( '/' ) ); ?>" class="space-y-3">
								<input type="search" 
									   name="s" 
									   value="<?php echo esc_attr( get_search_query() ); ?>" 
									   placeholder="Search again..." 
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
								<button type="submit" 
										class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-md transition-colors text-sm flex items-center justify-center">
									<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
									</svg>
									Search Again
								</button>
							</form>
						</div>
						
						<?php // Ad Space 1 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_1' ); ?>
						
						<?php // Popular Categories ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Popular Categories</h3>
							<?php
							$popular_categories = get_categories(array(
								'orderby' => 'count',
								'order' => 'DESC',
								'hide_empty' => true,
								'number' => 5
							));
							
							if (!empty($popular_categories)) :
							?>
								<div class="space-y-3">
									<?php foreach ($popular_categories as $pop_cat) : ?>
										<div class="flex items-center justify-between group">
											<a href="<?php echo get_category_link($pop_cat->term_id); ?>" 
											   class="text-gray-700 hover:text-green-600 transition-colors font-medium">
												<?php echo esc_html($pop_cat->name); ?>
											</a>
											<span class="text-sm text-gray-500"><?php echo $pop_cat->count; ?></span>
										</div>
									<?php endforeach; ?>
								</div>
							<?php else : ?>
								<p class="text-gray-500 text-sm">No categories found.</p>
							<?php endif; ?>
						</div>
						
						<?php // Recent Posts ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Recent Articles</h3>
							<?php
							$recent_posts = get_posts(array(
								'numberposts' => 4,
								'post_status' => 'publish'
							));
							
							if (!empty($recent_posts)) :
							?>
								<div class="space-y-4">
									<?php foreach ($recent_posts as $recent_post) : ?>
										<div class="group">
											<a href="<?php echo get_permalink($recent_post->ID); ?>" class="block">
												<div class="flex space-x-3">
													<?php if (has_post_thumbnail($recent_post->ID)) : ?>
														<div class="flex-shrink-0">
															<img src="<?php echo get_the_post_thumbnail_url($recent_post->ID, 'thumbnail'); ?>" 
																 alt="<?php echo esc_attr(get_the_title($recent_post->ID)); ?>" 
																 class="w-16 h-16 object-cover rounded-md">
														</div>
													<?php endif; ?>
													<div class="flex-1 min-w-0">
														<h4 class="text-sm font-semibold text-gray-900 group-hover:text-green-600 leading-tight line-clamp-3 mb-1">
															<?php echo get_the_title($recent_post->ID); ?>
														</h4>
														<p class="text-xs text-gray-500">
															<?php echo get_the_date('M j, Y', $recent_post->ID); ?>
														</p>
													</div>
												</div>
											</a>
										</div>
									<?php endforeach; ?>
								</div>
							<?php else : ?>
								<p class="text-gray-500 text-sm">No recent posts found.</p>
							<?php endif; 
							wp_reset_postdata();
							?>
						</div>
						
						<?php // Ad Space 2 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_2' ); ?>
						
						<?php // Newsletter Signup ?>
						<div class="bg-gray-800 rounded-lg p-6 text-white">
							<h3 class="text-lg font-bold mb-4">Stay In The Know</h3>
							<p class="text-gray-300 mb-4 text-sm">Get the latest sports news and updates delivered straight to your inbox.</p>
							<form class="space-y-3">
								<input type="email" placeholder="Your email" 
									   class="w-full px-3 py-2 bg-gray-700 text-white placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-sm">
								<button type="submit" 
										class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-md transition-colors text-sm">
									Subscribe
								</button>
							</form>
						</div>
						
						<?php // Ad Space 3 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_3' ); ?>
						
					</div>
				</div>
			</div>

		<?php else : ?>
			
			<?php // No Search Results Found ?>
			<div class="text-center py-16">
				<div class="max-w-md mx-auto">
					<svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
					</svg>
					<h2 class="text-2xl font-bold text-gray-900 mb-2">No Results Found</h2>
					<p class="text-gray-600 mb-6">
						Sorry, we couldn't find any articles matching 
						<span class="font-semibold">"<?php echo esc_html(get_search_query()); ?>"</span>
					</p>
					
					<div class="space-y-4">
						<div class="text-sm text-gray-500">
							<p>Try:</p>
							<ul class="mt-2 space-y-1">
								<li>• Different keywords</li>
								<li>• More general search terms</li>
								<li>• Checking your spelling</li>
							</ul>
						</div>
						
						<form role="search" method="get" action="<?php echo esc_url( home_url( '/' ) ); ?>" class="max-w-sm mx-auto">
							<div class="flex">
								<input type="search" 
									   name="s" 
									   placeholder="Try a new search..." 
									   class="flex-1 px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
								<button type="submit" 
										class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-r-md transition-colors">
									<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
									</svg>
								</button>
							</div>
						</form>
						
						<div class="mt-6">
							<a href="<?php echo home_url(); ?>" 
							   class="inline-flex items-center bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">
								<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
								</svg>
								Back to Homepage
							</a>
						</div>
					</div>
				</div>
			</div>
			
		<?php endif; ?>
		
	</div>
</main><!-- #main -->

<?php
get_footer();
