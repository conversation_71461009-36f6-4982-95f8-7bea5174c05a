<?php
/**
 * The template for displaying category archive pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package LWOS_2025
 */

get_header();
?>

<main id="primary" class="site-main">
	<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		
		<?php if ( have_posts() ) : ?>
			<?php
			// Get category information
			$category = get_queried_object();
			$category_count = $category->count;
			$category_description = category_description();
			?>
			
			<?php // Category Header Section ?>
			<div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg overflow-hidden mb-12">
				<div class="px-6 py-12 md:px-12">
					<div class="text-center">
						<div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
							</svg>
						</div>
						
						<h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
							<?php echo esc_html($category->name); ?>
						</h1>
						
						<?php if ( $category_description ) : ?>
							<div class="text-xl text-blue-100 mb-6 leading-relaxed max-w-3xl mx-auto">
								<?php echo wp_kses_post($category_description); ?>
							</div>
						<?php endif; ?>
						
						<div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-blue-100">
							<div class="flex items-center">
								<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.901-6.063 2.379C5.482 17.56 5.99 18 6.5 18h11c.51 0 1.018-.44.563-1.621z"></path>
								</svg>
								<span class="font-semibold"><?php echo $category_count; ?> Articles</span>
							</div>
							
							<div class="flex items-center">
								<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								<span>Updated Daily</span>
							</div>
							
							<?php if ( $category->parent ) : 
								$parent_category = get_category($category->parent);
							?>
								<div class="flex items-center">
									<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
									</svg>
									<span>
										Part of 
										<a href="<?php echo get_category_link($parent_category->term_id); ?>" 
										   class="hover:text-white font-medium transition-colors">
											<?php echo esc_html($parent_category->name); ?>
										</a>
									</span>
								</div>
							<?php endif; ?>
						</div>
					</div>
				</div>
			</div>
			
			<?php // Ad Section - Ad Inserter Block 10 ?>
			<section class="mb-12">
            <div class="max-w-4xl mx-auto">
                <!-- Desktop Player -->
                <div id="lwos_stn_player" class="hidden md:block">
                    <div id="FreeStarVideoAdContainer">
                        <div id="freestar-video-parent">
                            <div id="freestar-video-child"></div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Player -->
                <div id="lwos_stn_player_mobile" class="block md:hidden">
                    <div id="FreeStarVideoAdContainer">
                        <div id="freestar-video-parent">
                            <div id="freestar-video-child"></div>
                        </div>
                    </div>
                </div>
            </div>
			</section>


			<?php // Main Content Layout ?>
			<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
				
				<?php // Category Posts (3/4 width) ?>
				<div class="lg:col-span-3">
					<div class="flex items-center justify-between mb-8">
						<h2 class="text-3xl font-bold text-gray-900">
							Latest in <?php echo esc_html($category->name); ?>
						</h2>
						<div class="text-sm text-gray-500">
							<?php
							global $wp_query;
							$total_posts = $wp_query->found_posts;
							$paged = get_query_var('paged') ? get_query_var('paged') : 1;
							$posts_per_page = get_query_var('posts_per_page');
							$start = ($paged - 1) * $posts_per_page + 1;
							$end = min($start + $posts_per_page - 1, $total_posts);
							
							echo "Showing {$start}-{$end} of {$total_posts} articles";
							?>
						</div>
					</div>
					
					<?php // Posts Grid ?>
					<div class="space-y-8">
						<?php while ( have_posts() ) : the_post(); ?>
							<article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
								<div class="md:flex">
									<div class="md:w-1/3">
										<?php if ( has_post_thumbnail() ) : ?>
											<a href="<?php the_permalink(); ?>">
												<?php the_post_thumbnail('medium_large', array('class' => 'w-full h-48 md:h-full object-cover group-hover:scale-105 transition-transform duration-300')); ?>
											</a>
										<?php else : ?>
											<div class="w-full h-48 md:h-full bg-gray-200 flex items-center justify-center">
												<svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
												</svg>
											</div>
										<?php endif; ?>
									</div>
									
									<div class="md:w-2/3 p-6">
										<div class="flex items-center text-sm text-gray-500 mb-3 space-x-4">
											<span class="flex items-center">
												<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
												</svg>
												<?php echo get_the_date('F j, Y'); ?>
											</span>
											<span class="flex items-center">
												<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
												</svg>
												By&nbsp;<?php the_author_posts_link(); ?>
											</span>
											<span class="flex items-center">
												<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
												</svg>
												<?php comments_number('0', '1', '%'); ?>
											</span>
										</div>
										
										<h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
											<a href="<?php the_permalink(); ?>">
												<?php the_title(); ?>
											</a>
										</h3>
										
										<p class="text-gray-600 mb-4 leading-relaxed">
											<?php echo wp_trim_words(get_the_excerpt(), 25); ?>
										</p>
										
										<div class="flex items-center justify-between">
											<a href="<?php the_permalink(); ?>" 
											   class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors">
												Read Full Article
												<svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
												</svg>
											</a>
											
											<?php if ( has_tag() ) : ?>
												<div class="flex flex-wrap gap-1">
													<?php
													$tags = get_the_tags();
													if ( $tags ) {
														$tag_count = 0;
														foreach ( $tags as $tag ) {
															if ( $tag_count >= 2 ) break; // Limit to 2 tags
															echo '<span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">' . $tag->name . '</span>';
															$tag_count++;
														}
													}
													?>
												</div>
											<?php endif; ?>
										</div>
									</div>
								</div>
							</article>
						<?php endwhile; ?>
					</div>
					
					<?php // Pagination ?>
					<div class="mt-12">
						<?php
						the_posts_pagination(array(
							'prev_text' => '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>Previous',
							'next_text' => 'Next<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>',
							'before_page_number' => '<span class="px-4 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 transition-colors">',
							'after_page_number' => '</span>',
						));
						?>
					</div>
				</div>

				<?php // Right Sidebar (1/4 width) ?>
				<div class="lg:col-span-1">
					<div class="sticky top-8 space-y-6">
						
						<?php // Category Stats ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Category Stats</h3>
							<div class="space-y-3">
								<div class="flex items-center justify-between">
									<span class="text-gray-600">Total Articles</span>
									<span class="font-semibold text-gray-900"><?php echo $category_count; ?></span>
								</div>
								<div class="flex items-center justify-between">
									<span class="text-gray-600">Category</span>
									<span class="font-semibold text-gray-900"><?php echo esc_html($category->name); ?></span>
								</div>
								<?php if ( $category->parent ) : ?>
									<div class="flex items-center justify-between">
										<span class="text-gray-600">Parent</span>
										<a href="<?php echo get_category_link($parent_category->term_id); ?>" 
										   class="font-semibold text-blue-600 hover:text-blue-800 transition-colors">
											<?php echo esc_html($parent_category->name); ?>
										</a>
									</div>
								<?php endif; ?>
							</div>
						</div>
						
						<?php // Ad Space 1 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_1' ); ?>	
						
						<?php // Related Categories ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Related Categories</h3>
							<?php
							$related_categories = get_categories(array(
								'parent' => $category->parent ?: $category->term_id,
								'exclude' => $category->term_id,
								'hide_empty' => true,
								'number' => 5
							));
							
							if (!empty($related_categories)) :
							?>
								<div class="space-y-3">
									<?php foreach ($related_categories as $related_cat) : ?>
										<div class="flex items-center justify-between group">
											<a href="<?php echo get_category_link($related_cat->term_id); ?>" 
											   class="text-gray-700 hover:text-blue-600 transition-colors font-medium">
												<?php echo esc_html($related_cat->name); ?>
											</a>
											<span class="text-sm text-gray-500"><?php echo $related_cat->count; ?></span>
										</div>
									<?php endforeach; ?>
								</div>
							<?php else : ?>
								<p class="text-gray-500 text-sm">No related categories found.</p>
							<?php endif; ?>
						</div>
						
						<?php // Popular Posts in Category ?>
						<div class="bg-white rounded-lg shadow-md p-6">
							<h3 class="text-lg font-bold text-gray-900 mb-4">Popular in <?php echo esc_html($category->name); ?></h3>
							<?php
							$popular_posts = get_posts(array(
								'category' => $category->term_id,
								'numberposts' => 4,
								'meta_key' => 'post_views_count',
								'orderby' => 'meta_value_num',
								'order' => 'DESC'
							));
							
							if (!empty($popular_posts)) :
							?>
								<div class="space-y-4">
									<?php foreach ($popular_posts as $popular_post) : ?>
										<div class="group">
											<a href="<?php echo get_permalink($popular_post->ID); ?>" class="block">
												<div class="flex space-x-3">
													<?php if (has_post_thumbnail($popular_post->ID)) : ?>
														<div class="flex-shrink-0">
															<img src="<?php echo get_the_post_thumbnail_url($popular_post->ID, 'thumbnail'); ?>" 
																 alt="<?php echo esc_attr(get_the_title($popular_post->ID)); ?>" 
																 class="w-16 h-16 object-cover rounded-md">
														</div>
													<?php endif; ?>
													<div class="flex-1 min-w-0">
														<h4 class="text-sm font-semibold text-gray-900 group-hover:text-blue-600 leading-tight line-clamp-3 mb-1">
															<?php echo get_the_title($popular_post->ID); ?>
														</h4>
														<p class="text-xs text-gray-500">
															<?php echo get_the_date('M j, Y', $popular_post->ID); ?>
														</p>
													</div>
												</div>
											</a>
										</div>
									<?php endforeach; ?>
								</div>
							<?php else : ?>
								<p class="text-gray-500 text-sm">No popular posts found in this category.</p>
							<?php endif; 
							wp_reset_postdata();
							?>
						</div>
						
						<?php // Ad Space 2 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_2' ); ?>
						
						<?php // Newsletter Signup ?>
						<div class="bg-gray-800 rounded-lg p-6 text-white">
							<h3 class="text-lg font-bold mb-4">Stay Updated on <?php echo esc_html($category->name); ?></h3>
							<p class="text-gray-300 mb-4 text-sm">Get the latest <?php echo strtolower($category->name); ?> news delivered to your inbox.</p>
							<form class="space-y-3">
								<input type="email" placeholder="Your email" 
									   class="w-full px-3 py-2 bg-gray-700 text-white placeholder-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
								<button type="submit" 
										class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition-colors text-sm">
									Subscribe
								</button>
							</form>
						</div>
						
						<?php // Ad Space 3 ?>
						<?php do_action( 'lwos_ad_lwos_right_rail_3' ); ?>
						
					</div>
				</div>
			</div>

		<?php else : ?>
			
			<?php // No Posts Found ?>
			<div class="text-center py-16">
				<div class="max-w-md mx-auto">
					<svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.901-6.063 2.379C5.482 17.56 5.99 18 6.5 18h11c.51 0 1.018-.44.563-1.621z"></path>
					</svg>
					<h2 class="text-2xl font-bold text-gray-900 mb-2">No Articles Found</h2>
					<p class="text-gray-600">This category doesn't have any published articles yet.</p>
					<div class="mt-6">
						<a href="<?php echo home_url(); ?>" 
						   class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
							</svg>
							Back to Homepage
						</a>
					</div>
				</div>
			</div>
			
		<?php endif; ?>
		
	</div>
</main><!-- #main -->

<?php
get_footer(); 