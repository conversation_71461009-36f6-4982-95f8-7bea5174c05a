# LWOS 2025 – Ad Integration Guide

This project uses a **single, centralised system** to output all Freestar ads so that ad code only exists once in the code-base and templates remain clean & DRY.

---

## 1. Key files

| File | Purpose |
|------|---------|
| `inc/freestar-ads.php` | Stores the raw HTML for every Freestar slot **once** and registers WordPress hooks you can call from templates. |
| `functions.php` | Loads the file above so that the hooks are available site-wide. |
| Various templates (`header.php`, `front-page.php`, `single.php`, `footer.php`, etc.) | Call the hooks where an ad should render, e.g. `do_action( 'lwos_ad_lwos_header' );`. |

---

## 2. How it works

1. **Array of slots** – Inside `inc/freestar-ads.php` you will see a `$ad_slots` array.  
   • **Key** = the slot ID provided by Freestar (e.g. `lwos_header`).  
   • **Value** = the full HTML snippet given by Freestar.
2. **Hook registration** – For each array entry an action is registered: `lwos_ad_{slot_id}`.  
   • E.g. for `lwos_header` the action becomes `lwos_ad_lwos_header`.
3. **Template output** – Wherever you need the ad you simply call:

```php
do_action( 'lwos_ad_lwos_header' );
```

   WordPress runs the anonymous callback defined in `freestar-ads.php`, echoing the stored HTML.

---

## 3. Editing / Replacing ads

1. Open `inc/freestar-ads.php`.
2. Locate the relevant key in the `$ad_slots` array.
3. Replace its HTML string with the new snippet provided by your ad network.
4. Save & deploy – every template that calls this slot will now serve the new creative.

> No changes to templates are needed as long as the **slot ID stays the same**.

---

## 4. Adding a brand-new slot

1. Add a new element to the `$ad_slots` array with the new **slot ID** and HTML.
2. In the appropriate template file, drop in:

```php
do_action( 'lwos_ad_NEW_SLOT_ID' );
```

3. Done – WordPress will output the new ad.

---

## 5. Temporarily disabling a slot

• Either comment-out the array entry *or* comment-out the `do_action()` line in the template.  
• Keeping the entry but not calling it is cheapest; completely removing the entry prevents stray hooks from outputting.

---

## 6. Current slot → location map

| Slot ID | Hook name | Where it appears |
|---------|-----------|------------------|
| `lwos_header` | `lwos_ad_lwos_header` | Below the orange tagline bar in `header.php`.
| `lwos_billboard_BTF` | `lwos_ad_lwos_billboard_BTF` | Between featured section & main feed on `front-page.php`.
| `lwos_right_rail_1` | `lwos_ad_lwos_right_rail_1` | Right sidebar – top (front-page & single).
| `lwos_right_rail_2` | `lwos_ad_lwos_right_rail_2` | Right sidebar – mid (front-page & single).
| `lwos_right_rail_3` | `lwos_ad_lwos_right_rail_3` | Right sidebar – bottom (single post).
| `lwos_bouncex` | `lwos_ad_lwos_bouncex` | Just before `wp_footer()` in `footer.php`.
| `lwos_incontent_1..5` & `lwos_mpu_*` | `lwos_ad_{slot}` | Not yet placed – ready for future in-content injection.

*(Update this table when you add/remove slots.)*

---

### Happy monetising! 🎉 