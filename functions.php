<?php
/**
 * LWOS 2025 functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package LWOS_2025
 */

if ( ! defined( '_S_VERSION' ) ) {
	// Replace the version number of the theme on each release.
	define( '_S_VERSION', '1.0.1' );
}

/**
 * Get theme version for cache busting
 * Uses file modification time during development for automatic cache busting
 */
function lwos_2025_get_theme_version() {
	// For development: use file modification time for automatic cache busting
	if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
		return filemtime( get_template_directory() . '/style.css' );
	}
	// For production: use theme version
	return _S_VERSION;
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function lwos_2025_setup() {
	/*
		* Make theme available for translation.
		* Translations can be filed in the /languages/ directory.
		* If you're building a theme based on LWOS 2025, use a find and replace
		* to change 'lwos-2025' to the name of your theme in all the template files.
		*/
	load_theme_textdomain( 'lwos-2025', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
		* Let WordPress manage the document title.
		* By adding theme support, we declare that this theme does not use a
		* hard-coded <title> tag in the document head, and expect WordPress to
		* provide it for us.
		*/
	add_theme_support( 'title-tag' );

	/*
		* Enable support for Post Thumbnails on posts and pages.
		*
		* @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		*/
	add_theme_support( 'post-thumbnails' );

	// This theme uses wp_nav_menu() in one location.
	register_nav_menus(
		array(
			'menu-1' => esc_html__( 'Primary', 'lwos-2025' ),
		)
	);

	/*
		* Switch default core markup for search form, comment form, and comments
		* to output valid HTML5.
		*/
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	add_theme_support(
		'custom-background',
		apply_filters(
			'lwos_2025_custom_background_args',
			array(
				'default-color' => 'ffffff',
				'default-image' => '',
			)
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 250,
			'width'       => 250,
			'flex-width'  => true,
			'flex-height' => true,
		)
	);

	// Add custom 3:2 featured image sizes for consistent cropping.
	add_image_size( 'lwos_medium_3_2', 600, 400, true ); // 3:2 medium
	add_image_size( 'lwos_large_3_2', 1200, 800, true ); // 3:2 large
}
add_action( 'after_setup_theme', 'lwos_2025_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function lwos_2025_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'lwos_2025_content_width', 640 );
}
add_action( 'after_setup_theme', 'lwos_2025_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function lwos_2025_widgets_init() {
	register_sidebar(
		array(
			'name'          => esc_html__( 'Sidebar', 'lwos-2025' ),
			'id'            => 'sidebar-1',
			'description'   => esc_html__( 'Add widgets here.', 'lwos-2025' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', 'lwos_2025_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function lwos_2025_scripts() {
	wp_enqueue_style( 'lwos-2025-style', get_stylesheet_uri(), array(), lwos_2025_get_theme_version() );
	wp_style_add_data( 'lwos-2025-style', 'rtl', 'replace' );

	// wp_enqueue_script( 'lwos-2025-navigation', get_template_directory_uri() . '/js/navigation.js', array(), _S_VERSION, true ); // Commented out to prevent conflict

	// Main theme JavaScript for toggles, etc.
	wp_enqueue_script( 'lwos-2025-main', get_template_directory_uri() . '/js/main.js', array( 'jquery' ), lwos_2025_get_theme_version(), true );

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}
}
add_action( 'wp_enqueue_scripts', 'lwos_2025_scripts' );

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
	require get_template_directory() . '/inc/jetpack.php';
}

/**
 * Load Custom Nav Walker for Desktop Menu.
 */
require get_template_directory() . '/inc/LWOS_Desktop_Nav_Walker.php';

/**
 * Custom comment callback for styled comments
 */
function lwos_2025_comment_callback( $comment, $args, $depth ) {
	$tag = ( 'div' === $args['style'] ) ? 'div' : 'li';
	?>
	<<?php echo $tag; ?> id="comment-<?php comment_ID(); ?>" <?php comment_class( 'comment bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow' ); ?>>
		<div class="comment-body">
			<div class="flex items-start space-x-4">
				<div class="flex-shrink-0">
					<?php if ( 0 != $args['avatar_size'] ) : ?>
						<div class="relative">
							<?php echo get_avatar( $comment, $args['avatar_size'], '', '', array( 'class' => 'rounded-full border-2 border-gray-200' ) ); ?>
							<?php if ( '1' == $comment->comment_approved ) : ?>
								<div class="absolute -bottom-1 -right-1 w-4 h-4 bg-[rgb(255,136,0)] rounded-full flex items-center justify-center">
									<svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
									</svg>
								</div>
							<?php endif; ?>
						</div>
					<?php endif; ?>
				</div>
				
				<div class="flex-1 min-w-0">
					<div class="flex items-center justify-between mb-2">
						<div class="flex items-center space-x-2">
							<h4 class="comment-author text-lg font-semibold text-gray-900">
								<?php
								$author_url = get_comment_author_url( $comment );
								$author_name = get_comment_author( $comment );
								if ( $author_url && $author_url !== 'http://' ) {
									echo '<a href="' . esc_url( $author_url ) . '" class="text-[rgb(255,136,0)] hover:text-orange-600 transition-colors">' . esc_html( $author_name ) . '</a>';
								} else {
									echo esc_html( $author_name );
								}
								?>
							</h4>
							<?php if ( user_can( $comment->user_id, 'edit_posts' ) ) : ?>
								<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[rgb(255,136,0)] text-white">
									<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
									</svg>
									Author
								</span>
							<?php endif; ?>
						</div>
						
						<div class="flex items-center space-x-2 text-sm text-gray-500">
							<time datetime="<?php comment_time( 'c' ); ?>" class="flex items-center">
								<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								<?php
								printf(
									'<a href="%1$s" class="hover:text-[rgb(255,136,0)] transition-colors">%2$s</a>',
									esc_url( get_comment_link( $comment, $args, $depth ) ),
									sprintf( '%1$s at %2$s', get_comment_date( '', $comment ), get_comment_time() )
								);
								?>
							</time>
						</div>
					</div>
					
					<?php if ( '0' == $comment->comment_approved ) : ?>
						<div class="mb-4 p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded">
							<p class="text-sm text-yellow-700 font-medium flex items-center">
								<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
								</svg>
								<?php esc_html_e( 'Your comment is awaiting moderation.', 'lwos-2025' ); ?>
							</p>
						</div>
					<?php endif; ?>
					
					<div class="comment-content text-gray-700 leading-relaxed mb-4">
						<?php comment_text(); ?>
					</div>
					
					<div class="comment-meta flex items-center justify-between">
						<div class="flex items-center space-x-4">
							<?php
							comment_reply_link(
								array_merge(
									$args,
									array(
										'add_below' => 'comment',
										'depth'     => $depth,
										'max_depth' => $args['max_depth'],
										'before'    => '<span class="reply inline-flex items-center text-sm text-gray-600 hover:text-[rgb(255,136,0)] transition-colors cursor-pointer">',
										'after'     => '</span>',
									)
								)
							);
							?>
							<?php if ( current_user_can( 'edit_comment', $comment->comment_ID ) ) : ?>
								<a href="<?php echo esc_url( get_edit_comment_link( $comment ) ); ?>" class="inline-flex items-center text-sm text-gray-600 hover:text-[rgb(255,136,0)] transition-colors">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
									</svg>
									Edit
								</a>
							<?php endif; ?>
						</div>
						
						<div class="flex items-center text-xs text-gray-400">
							<span class="flex items-center">
								<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
								</svg>
								#<?php comment_ID(); ?>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	<?php
}

/**
 * Get posts from all sites in the multisite network
 * 
 * @param int $posts_per_page Number of posts to retrieve
 * @return array Array of post objects from all network sites
 */
function lwos_get_network_posts( $posts_per_page = 30 ) {
	if ( ! is_multisite() ) {
		// Fallback to regular posts if not multisite
		return get_posts( array(
			'posts_per_page' => $posts_per_page,
			'post_status' => 'publish'
		) );
	}
	
	// Create a unique cache key based on the number of posts requested
	$cache_key = 'lwos_network_posts_' . $posts_per_page;
	
	// Try to get cached posts first
	$cached_posts = get_transient( $cache_key );
	if ( $cached_posts !== false ) {
		// Return cached posts if available and not expired
		return $cached_posts;
	}
	
	global $wpdb;
	$network_posts = array();
	
	// Get all sites in the network
	$sites = get_sites( array(
		'public' => 1,
		'archived' => 0,
		'spam' => 0,
		'deleted' => 0,
		'number' => 100 // Adjust if you have more than 100 sites
	) );
	
	foreach ( $sites as $site ) {
		// Switch to each site
		switch_to_blog( $site->blog_id );
		
		// Get posts from this site
		$site_posts = get_posts( array(
			'posts_per_page' => $posts_per_page, // Get more from each site, we'll sort later
			'post_status' => 'publish',
			'date_query'  => array(
				array(
					'after' => '2020-12-31', // Only posts from 2021 onwards
					'inclusive' => true,
				),
			),
			'meta_query' => array(
				'relation' => 'OR',
				array(
					'key' => '_lwos_exclude_from_network',
					'compare' => 'NOT EXISTS'
				),
				array(
					'key' => '_lwos_exclude_from_network',
					'value' => '1',
					'compare' => '!='
				)
			)
		) );
		
		// Add site info and fetch all necessary data while in the correct site context
		foreach ( $site_posts as $post ) {
			$post->site_id = $site->blog_id;
			$post->site_url = get_home_url( $site->blog_id );
			$post->site_name = get_bloginfo( 'name' );
			
			// Ensure permalink is correct for the site
			$post->permalink = get_permalink( $post->ID );
			
			// Get featured image data while in the correct site context
			if ( has_post_thumbnail( $post->ID ) ) {
				$post->featured_image_url = get_the_post_thumbnail_url( $post->ID, 'lwos_medium_3_2' );
				$post->featured_image_url_large = get_the_post_thumbnail_url( $post->ID, 'lwos_large_3_2' );
				$post->featured_image_url_full = get_the_post_thumbnail_url( $post->ID, 'full' );
				$post->has_thumbnail = true;
			} else {
				$post->featured_image_url = '';
				$post->featured_image_url_large = '';
				$post->featured_image_url_full = '';
				$post->has_thumbnail = false;
			}
			
			// Get excerpt while in the correct site context
			$post->post_excerpt_processed = get_the_excerpt( $post );
			if ( empty( $post->post_excerpt_processed ) ) {
				$post->post_excerpt_processed = wp_trim_words( $post->post_content, 55 );
			}
			
			// Get formatted date
			$post->formatted_date = get_the_date( 'F j, Y', $post );
			
			// Get author information
			$author_id = $post->post_author;
			$post->author_name = get_the_author_meta( 'display_name', $author_id );
			$post->author_url = get_author_posts_url( $author_id );
			
			// Get categories and tags
			$categories = get_the_category( $post->ID );
			$post->categories = $categories;
			$post->primary_category = !empty( $categories ) ? $categories[0]->name : '';
			
			$tags = get_the_tags( $post->ID );
			$post->tags = $tags ? $tags : array();
			
			// Get comment count
			$post->comment_count = get_comments_number( $post->ID );
			
			$network_posts[] = $post;
		}
		
		// Restore to original site
		restore_current_blog();
	}
	
	// Sort all posts by date (newest first)
	usort( $network_posts, function( $a, $b ) {
		return strtotime( $b->post_date ) - strtotime( $a->post_date );
	} );
	
	// Return only the requested number of posts
	$final_posts = array_slice( $network_posts, 0, $posts_per_page );
	
	// Cache the results for 2 minutes (120 seconds)
	set_transient( $cache_key, $final_posts, 120 );
	
	return $final_posts;
}

/**
 * Clear network posts cache when posts are published, updated, or deleted
 * This ensures fresh content appears quickly while maintaining cache performance
 */
function lwos_clear_network_posts_cache() {
	// Clear all possible cache variations
	$post_counts = array( 30, 20, 15, 10, 5 ); // Common post count variations
	
	foreach ( $post_counts as $count ) {
		$cache_key = 'lwos_network_posts_' . $count;
		delete_transient( $cache_key );
	}
}

/**
 * Clear cache when post status changes (publish, draft, etc.)
 */
function lwos_clear_network_posts_cache_on_post_change( $new_status, $old_status, $post ) {
	// Only clear cache for published posts or posts being published/unpublished
	if ( $new_status === 'publish' || $old_status === 'publish' ) {
		lwos_clear_network_posts_cache();
		
		// Prime the cache immediately after clearing it
		lwos_prime_network_posts_cache();
	}
}

// Also clear cache when posts are deleted
add_action( 'delete_post', 'lwos_clear_network_posts_cache' );

/**
 * Prime the network posts cache by pre-loading common post count variations
 * This ensures the cache is ready for immediate use after content changes
 */
function lwos_prime_network_posts_cache() {
	// Only prime cache on the main site (site ID 2) to avoid unnecessary work
	if ( get_current_blog_id() !== 2 && is_multisite() ) {
		return;
	}
	
	// Common post count variations used throughout the site
	$post_counts_to_prime = array( 30, 20, 15, 10, 5 );
	
	foreach ( $post_counts_to_prime as $count ) {
		// Use a background process to avoid slowing down the post save
		wp_schedule_single_event( time(), 'lwos_prime_cache_background', array( $count ) );
	}
}

// Register the background cache priming action
add_action( 'lwos_prime_cache_background', 'lwos_prime_cache_for_count' );

/**
 * Background function to prime cache for a specific post count
 * 
 * @param int $post_count Number of posts to cache
 */
function lwos_prime_cache_for_count( $post_count ) {
	// Switch to the main site context for network queries
	if ( is_multisite() ) {
		switch_to_blog( 2 );
	}
	
	// This will trigger the network query and cache the results
	lwos_get_network_posts( $post_count );
	
	// Restore original blog context
	if ( is_multisite() ) {
		restore_current_blog();
	}
}

// Also prime cache on theme activation or when the function is first loaded
add_action( 'after_switch_theme', 'lwos_prime_network_posts_cache' );

// Add a manual cache prime function for admin use
add_action( 'wp_ajax_lwos_prime_network_cache', 'lwos_admin_prime_network_cache' );

/**
 * Admin AJAX function to manually prime network posts cache
 */
function lwos_admin_prime_network_cache() {
	if ( ! current_user_can( 'manage_options' ) ) {
		wp_die( 'Unauthorized' );
	}
	
	// Clear existing cache first
	lwos_clear_network_posts_cache();
	
	// Prime the cache
	lwos_prime_network_posts_cache();
	
	wp_send_json_success( 'Network posts cache primed successfully.' );
}

// Add admin function to manually clear cache if needed
add_action( 'wp_ajax_lwos_clear_network_cache', 'lwos_admin_clear_network_cache' );

/**
 * Admin AJAX function to manually clear network posts cache
 */
function lwos_admin_clear_network_cache() {
	if ( ! current_user_can( 'manage_options' ) ) {
		wp_die( 'Unauthorized' );
	}
	
	lwos_clear_network_posts_cache();
	wp_send_json_success( 'Network posts cache cleared successfully.' );
}

// Hook into post status changes to clear cache
add_action( 'transition_post_status', 'lwos_clear_network_posts_cache_on_post_change', 10, 3 );

require get_template_directory() . '/inc/freestar-ads.php';

/* add_action('wp_footer', 'logrocket_init_for_logged_in_users');
add_action('admin_footer', 'logrocket_init_for_logged_in_users');

function logrocket_init_for_logged_in_users() {
    if ( is_user_logged_in() ) {
        $current_user = wp_get_current_user();
        $user_id = $current_user->ID;
        $user_name = esc_js($current_user->user_login);
        $user_email = esc_js($current_user->user_email);
        $site_url = esc_js(home_url()); // e.g., https://lastwordonsports.com/some-subsite

        ?>
        <script src="https://cdn.lgrckt-in.com/LogRocket.min.js" crossorigin="anonymous"></script>
        <script>
            if (window.LogRocket) {
                LogRocket.init('lwos/lwos');

                LogRocket.identify('<?php echo $user_id; ?>', {
                    name: '<?php echo $user_name; ?>',
                    email: '<?php echo $user_email; ?>',
                    siteURL: '<?php echo $site_url; ?>'
                });
            }
        </script>
        <?php
    }
} */
/**
 * Insert STN Player after the first paragraph on single posts
 */
function lwos_insert_stn_player_after_first_paragraph( $content ) {
    if ( is_single() && in_the_loop() && is_main_query() ) {
        $player_html = '
        <section class="mb-12">
            <div class="max-w-4xl mx-auto">
                <!-- Desktop Player -->
                <div id="lwos_stn_player" class="hidden md:block">
                    <div id="FreeStarVideoAdContainer">
                        <div id="freestar-video-parent">
                            <div id="freestar-video-child"></div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Player -->
                <div id="lwos_stn_player_mobile" class="block md:hidden">
                    <div id="FreeStarVideoAdContainer">
                        <div id="freestar-video-parent">
                            <div id="freestar-video-child"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>';

        // Split the content into paragraphs
        $paragraphs = explode( '</p>', $content );

        if ( ! empty( $paragraphs[0] ) ) {
            // Append player after the first paragraph
            $paragraphs[0] .= '</p>' . $player_html;
        }

        // Rebuild content
        $content = implode( '</p>', $paragraphs );
    }

    return $content;
}
add_filter( 'the_content', 'lwos_insert_stn_player_after_first_paragraph' );

/**
 * Determine if current site is the Hockey subsite (subdirectory: /hockey)
 */
function lwos_is_hockey_site() {
    if ( ! is_multisite() ) {
        return false;
    }
    $home_url = home_url( '/' );
    $path = wp_parse_url( $home_url, PHP_URL_PATH );
    if ( ! is_string( $path ) ) {
        return false;
    }
    // Match "/hockey" or "/hockey/" at the start of the path for subdirectory multisite
    $normalized = rtrim( $path, '/' );
    return ($normalized === '/hockey') || (strpos( $path, '/hockey/' ) === 0);
}

/**
 * Render Hockey right-rail top ad (300x600) when on the Hockey subsite
 * Hooked into the standard right rail top ad slot.
 */
function lwos_render_hockey_right_rail_top_ad() {
    if ( ! lwos_is_hockey_site() ) {
        return;
    }

    echo do_shortcode( '[adinserter name="Amazon Ad"]' );
}
add_action( 'lwos_ad_lwos_right_rail_1', 'lwos_render_hockey_right_rail_top_ad', 5 );

/**
 * Restrict Contributors from adding/removing categories or tags in post editor.
 * Editors and Admins retain full access.
 */
function restrict_contributor_taxonomies_editor() {
    // Only apply to Contributors
    if (!current_user_can('contributor') || current_user_can('editor') || current_user_can('administrator')) {
        return;
    }

    // Remove the ability to manage categories and tags
    $role = get_role('contributor');
    if ($role) {
        $role->remove_cap('manage_categories');
        $role->remove_cap('manage_post_tags');
    }

    // Modify meta boxes in post editor
    add_action('add_meta_boxes', function() {
        global $post;

        if (!current_user_can('contributor')) return;

        // Categories
        remove_meta_box('categorydiv', 'post', 'side');
        add_meta_box(
            'categorydiv_readonly',          // ID
            'Categories',                    // Title
            'display_readonly_categories',   // Callback
            'post',                          // Post type
            'side',                          // Context
            'default'                        // Priority
        );

        // Tags
        remove_meta_box('tagsdiv-post_tag', 'post', 'side');
        add_meta_box(
            'tagsdiv_post_tag_readonly',
            'Tags',
            'display_readonly_tags',
            'post',
            'side',
            'default'
        );
    });
}
add_action('init', 'restrict_contributor_taxonomies_editor');

/**
 * Display selected categories as read-only
 */
function display_readonly_categories($post) {
    $categories = wp_get_post_categories($post->ID, ['fields' => 'names']);
    echo '<p><em>Editors will add the Categories.</em></p>';
    if ($categories) {
        echo '<ul>';
        foreach ($categories as $cat) {
            echo '<li>' . esc_html($cat) . '</li>';
        }
        echo '</ul>';
    } else {
        echo '<p><em>No category assigned yet.</em></p>';
    }
}

/**
 * Display selected tags as read-only
 */
function display_readonly_tags($post) {
    $tags = wp_get_post_tags($post->ID, ['fields' => 'names']);
    echo '<p><em>Editors will add the Tags.</em></p>';
    if ($tags) {
        echo '<ul>';
        foreach ($tags as $tag) {
            echo '<li>' . esc_html($tag) . '</li>';
        }
        echo '</ul>';
    } else {
        echo '<p><em>No tags assigned yet.</em></p>';
    }
}


